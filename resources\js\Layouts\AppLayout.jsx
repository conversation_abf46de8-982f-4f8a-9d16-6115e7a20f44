import React from 'react';
import { Head, Link, usePage } from '@inertiajs/react';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';

export default function AppLayout({ children, title = 'Eagle\'s Club' }) {
    const { auth } = usePage().props;
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

    const navigation = [
        { name: 'Home', href: '/' },
        { name: 'Events', href: '/events' },
        { name: 'Announcements', href: '/announcements' },
        { name: 'About', href: '/about' },
        { name: 'Contact', href: '/contact' },
    ];

    const userNavigation = auth.user ? [
        { name: 'Dashboard', href: '/dashboard' },
        { name: 'Profile', href: '/profile' },
        { name: 'Logout', href: '/logout', method: 'post' },
    ] : [
        { name: 'Login', href: '/login' },
        { name: 'Register', href: '/register' },
    ];

    return (
        <>
            <Head title={title} />
            <div className="min-h-screen bg-gray-50">
                {/* Navigation */}
                <nav className="bg-white shadow-lg">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex justify-between h-16">
                            <div className="flex items-center">
                                <Link href="/" className="flex items-center">
                                    <span className="text-2xl font-bold text-amber-600">🦅</span>
                                    <span className="ml-2 text-xl font-bold text-gray-900">Eagle's Club</span>
                                </Link>
                            </div>

                            {/* Desktop Navigation */}
                            <div className="hidden md:flex items-center space-x-8">
                                {navigation.map((item) => (
                                    <Link
                                        key={item.name}
                                        href={item.href}
                                        className="text-gray-700 hover:text-amber-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                                    >
                                        {item.name}
                                    </Link>
                                ))}
                            </div>

                            {/* User Navigation */}
                            <div className="hidden md:flex items-center space-x-4">
                                {auth.user ? (
                                    <div className="flex items-center space-x-4">
                                        <span className="text-gray-700">Welcome, {auth.user.name}</span>
                                        {userNavigation.map((item) => (
                                            <Link
                                                key={item.name}
                                                href={item.href}
                                                method={item.method || 'get'}
                                                as={item.method ? 'button' : 'a'}
                                                className="text-gray-700 hover:text-amber-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                                            >
                                                {item.name}
                                            </Link>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="flex items-center space-x-4">
                                        <Link
                                            href="/login"
                                            className="text-gray-700 hover:text-amber-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                                        >
                                            Login
                                        </Link>
                                        <Link
                                            href="/register"
                                            className="bg-amber-600 text-white hover:bg-amber-700 px-4 py-2 rounded-md text-sm font-medium transition-colors"
                                        >
                                            Join Club
                                        </Link>
                                    </div>
                                )}
                            </div>

                            {/* Mobile menu button */}
                            <div className="md:hidden flex items-center">
                                <button
                                    onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                                    className="text-gray-700 hover:text-amber-600 p-2"
                                >
                                    {mobileMenuOpen ? (
                                        <XMarkIcon className="h-6 w-6" />
                                    ) : (
                                        <Bars3Icon className="h-6 w-6" />
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Mobile Navigation */}
                    {mobileMenuOpen && (
                        <div className="md:hidden">
                            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                                {navigation.map((item) => (
                                    <Link
                                        key={item.name}
                                        href={item.href}
                                        className="text-gray-700 hover:text-amber-600 block px-3 py-2 rounded-md text-base font-medium"
                                    >
                                        {item.name}
                                    </Link>
                                ))}
                                <div className="border-t pt-4">
                                    {userNavigation.map((item) => (
                                        <Link
                                            key={item.name}
                                            href={item.href}
                                            method={item.method || 'get'}
                                            as={item.method ? 'button' : 'a'}
                                            className="text-gray-700 hover:text-amber-600 block px-3 py-2 rounded-md text-base font-medium"
                                        >
                                            {item.name}
                                        </Link>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}
                </nav>

                {/* Main Content */}
                <main>{children}</main>

                {/* Footer */}
                <footer className="bg-gray-800 text-white">
                    <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                            <div className="col-span-1 md:col-span-2">
                                <div className="flex items-center mb-4">
                                    <span className="text-2xl font-bold text-amber-400">🦅</span>
                                    <span className="ml-2 text-xl font-bold">Eagle's Club</span>
                                </div>
                                <p className="text-gray-300 mb-4">
                                    A premier club dedicated to excellence, leadership, and community service.
                                    Join us in soaring to new heights together.
                                </p>
                            </div>
                            <div>
                                <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
                                <ul className="space-y-2">
                                    <li><Link href="/about" className="text-gray-300 hover:text-amber-400">About Us</Link></li>
                                    <li><Link href="/events" className="text-gray-300 hover:text-amber-400">Events</Link></li>
                                    <li><Link href="/membership" className="text-gray-300 hover:text-amber-400">Membership</Link></li>
                                    <li><Link href="/contact" className="text-gray-300 hover:text-amber-400">Contact</Link></li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="text-lg font-semibold mb-4">Contact Info</h3>
                                <ul className="space-y-2 text-gray-300">
                                    <li>📧 <EMAIL></li>
                                    <li>📞 +63 123 456 7890</li>
                                    <li>📍 Manila, Philippines</li>
                                </ul>
                            </div>
                        </div>
                        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
                            <p>&copy; 2025 Eagle's Club. All rights reserved.</p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
