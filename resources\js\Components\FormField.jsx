import React from 'react';

export default function FormField({ 
    label, 
    id, 
    type = 'text', 
    value, 
    onChange, 
    error, 
    required = false, 
    placeholder,
    options = [],
    rows = 3,
    className = '',
    ...props 
}) {
    const baseInputClasses = "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-amber-500 focus:border-amber-500 sm:text-sm";
    const errorClasses = error ? "border-red-300 focus:ring-red-500 focus:border-red-500" : "";
    const inputClasses = `${baseInputClasses} ${errorClasses} ${className}`;

    const renderInput = () => {
        switch (type) {
            case 'textarea':
                return (
                    <textarea
                        id={id}
                        value={value}
                        onChange={onChange}
                        rows={rows}
                        className={inputClasses}
                        placeholder={placeholder}
                        required={required}
                        {...props}
                    />
                );
            case 'select':
                return (
                    <select
                        id={id}
                        value={value}
                        onChange={onChange}
                        className={inputClasses}
                        required={required}
                        {...props}
                    >
                        {placeholder && <option value="">{placeholder}</option>}
                        {options.map((option) => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                );
            case 'checkbox':
                return (
                    <div className="flex items-center">
                        <input
                            type="checkbox"
                            id={id}
                            checked={value}
                            onChange={onChange}
                            className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded"
                            {...props}
                        />
                        <label htmlFor={id} className="ml-2 block text-sm text-gray-900">
                            {label}
                        </label>
                    </div>
                );
            default:
                return (
                    <input
                        type={type}
                        id={id}
                        value={value}
                        onChange={onChange}
                        className={inputClasses}
                        placeholder={placeholder}
                        required={required}
                        {...props}
                    />
                );
        }
    };

    if (type === 'checkbox') {
        return (
            <div>
                {renderInput()}
                {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
            </div>
        );
    }

    return (
        <div>
            <label htmlFor={id} className="block text-sm font-medium text-gray-700">
                {label} {required && <span className="text-red-500">*</span>}
            </label>
            {renderInput()}
            {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
        </div>
    );
}
