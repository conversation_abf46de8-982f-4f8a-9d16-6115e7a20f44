import React from 'react';
import AppLayout from '../Layouts/AppLayout';
import { usePage } from '@inertiajs/react';
import { 
    UserIcon, 
    CalendarDaysIcon, 
    SpeakerWaveIcon, 
    CreditCardIcon,
    CheckCircleIcon,
    ClockIcon,
    XCircleIcon
} from '@heroicons/react/24/outline';

export default function Dashboard() {
    const { auth } = usePage().props;
    const user = auth.user;

    const getStatusColor = (status) => {
        switch (status) {
            case 'active':
                return 'text-green-600 bg-green-100';
            case 'pending':
                return 'text-yellow-600 bg-yellow-100';
            case 'inactive':
                return 'text-gray-600 bg-gray-100';
            case 'suspended':
                return 'text-red-600 bg-red-100';
            default:
                return 'text-gray-600 bg-gray-100';
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'active':
                return CheckCircleIcon;
            case 'pending':
                return ClockIcon;
            case 'suspended':
                return XCircleIcon;
            default:
                return ClockIcon;
        }
    };

    const StatusIcon = getStatusIcon(user.membership_status);

    const stats = [
        {
            name: 'Membership Status',
            value: user.membership_status.charAt(0).toUpperCase() + user.membership_status.slice(1),
            icon: StatusIcon,
            color: getStatusColor(user.membership_status),
        },
        {
            name: 'Member Since',
            value: user.membership_start_date 
                ? new Date(user.membership_start_date).toLocaleDateString()
                : 'Pending',
            icon: CalendarDaysIcon,
            color: 'text-blue-600 bg-blue-100',
        },
        {
            name: 'Profile Completion',
            value: '85%',
            icon: UserIcon,
            color: 'text-purple-600 bg-purple-100',
        },
        {
            name: 'Events Attended',
            value: '12',
            icon: CalendarDaysIcon,
            color: 'text-green-600 bg-green-100',
        },
    ];

    const quickActions = [
        {
            name: 'Update Profile',
            description: 'Edit your personal information and preferences',
            href: '/profile',
            icon: UserIcon,
            color: 'bg-blue-500',
        },
        {
            name: 'View Events',
            description: 'Browse upcoming club events and activities',
            href: '/events',
            icon: CalendarDaysIcon,
            color: 'bg-green-500',
        },
        {
            name: 'Announcements',
            description: 'Read the latest club news and announcements',
            href: '/announcements',
            icon: SpeakerWaveIcon,
            color: 'bg-yellow-500',
        },
        {
            name: 'Payment History',
            description: 'View your payment history and make new payments',
            href: '/payments',
            icon: CreditCardIcon,
            color: 'bg-purple-500',
        },
    ];

    return (
        <AppLayout title="Dashboard">
            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Welcome Section */}
                    <div className="bg-white overflow-hidden shadow rounded-lg mb-8">
                        <div className="px-4 py-5 sm:p-6">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="h-16 w-16 bg-amber-100 rounded-full flex items-center justify-center">
                                        <span className="text-2xl">🦅</span>
                                    </div>
                                </div>
                                <div className="ml-5">
                                    <h1 className="text-2xl font-bold text-gray-900">
                                        Welcome back, {user.name}!
                                    </h1>
                                    <p className="text-gray-600">
                                        {user.membership_status === 'active' 
                                            ? 'You are an active member of Eagle\'s Club'
                                            : user.membership_status === 'pending'
                                            ? 'Your membership application is under review'
                                            : 'Please contact admin for membership status'
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Stats Grid */}
                    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                        {stats.map((stat) => (
                            <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <div className={`p-3 rounded-md ${stat.color}`}>
                                                <stat.icon className="h-6 w-6" />
                                            </div>
                                        </div>
                                        <div className="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt className="text-sm font-medium text-gray-500 truncate">
                                                    {stat.name}
                                                </dt>
                                                <dd className="text-lg font-medium text-gray-900">
                                                    {stat.value}
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Quick Actions */}
                    <div className="bg-white shadow rounded-lg mb-8">
                        <div className="px-4 py-5 sm:p-6">
                            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                                Quick Actions
                            </h3>
                            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                                {quickActions.map((action) => (
                                    <a
                                        key={action.name}
                                        href={action.href}
                                        className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-amber-500 rounded-lg border border-gray-200 hover:border-amber-300 transition-colors"
                                    >
                                        <div>
                                            <span className={`rounded-lg inline-flex p-3 ${action.color} text-white`}>
                                                <action.icon className="h-6 w-6" />
                                            </span>
                                        </div>
                                        <div className="mt-4">
                                            <h3 className="text-lg font-medium text-gray-900">
                                                <span className="absolute inset-0" aria-hidden="true" />
                                                {action.name}
                                            </h3>
                                            <p className="mt-2 text-sm text-gray-500">
                                                {action.description}
                                            </p>
                                        </div>
                                        <span
                                            className="pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400"
                                            aria-hidden="true"
                                        >
                                            <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="m11.293 17.293 1.414 1.414L19.414 12l-6.707-6.707-1.414 1.414L15.586 11H5v2h10.586l-4.293 4.293z" />
                                            </svg>
                                        </span>
                                    </a>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Recent Activity */}
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-4 py-5 sm:p-6">
                            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                                Recent Activity
                            </h3>
                            <div className="text-center py-8">
                                <CalendarDaysIcon className="mx-auto h-12 w-12 text-gray-400" />
                                <h3 className="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    Your recent club activities will appear here.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
