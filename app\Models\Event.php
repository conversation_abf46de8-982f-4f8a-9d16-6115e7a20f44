<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'content',
        'location',
        'start_date',
        'end_date',
        'image_path',
        'is_featured',
        'is_public',
        'max_participants',
        'registration_fee',
        'registration_deadline',
        'status',
        'created_by',
    ];

    protected function casts(): array
    {
        return [
            'start_date' => 'datetime',
            'end_date' => 'datetime',
            'registration_deadline' => 'datetime',
            'is_featured' => 'boolean',
            'is_public' => 'boolean',
            'registration_fee' => 'decimal:2',
        ];
    }

    // Relationships
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Helper methods
    public function getImageUrlAttribute()
    {
        return $this->image_path ? asset('storage/' . $this->image_path) : null;
    }

    public function isUpcoming()
    {
        return $this->start_date > now();
    }

    public function isPast()
    {
        return $this->start_date < now();
    }

    public function isRegistrationOpen()
    {
        if ($this->registration_deadline) {
            return now() <= $this->registration_deadline;
        }
        return $this->isUpcoming();
    }
}
