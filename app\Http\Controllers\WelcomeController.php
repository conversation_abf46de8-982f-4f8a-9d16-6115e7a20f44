<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use App\Models\Event;
use App\Models\Announcement;

class WelcomeController extends Controller
{
    public function index()
    {
        $events = Event::where('status', 'published')
            ->where('is_public', true)
            ->where('start_date', '>=', now())
            ->orderBy('start_date')
            ->take(3)
            ->get();

        $announcements = Announcement::where('status', 'published')
            ->where(function($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>=', now());
            })
            ->orderBy('is_pinned', 'desc')
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        return Inertia::render('Welcome', [
            'events' => $events,
            'announcements' => $announcements,
        ]);
    }
}
