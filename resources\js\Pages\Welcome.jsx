import React from 'react';
import AppLayout from '../Layouts/AppLayout';
import { Link } from '@inertiajs/react';
import { 
    UserGroupIcon, 
    CalendarDaysIcon, 
    SpeakerWaveIcon, 
    CreditCardIcon,
    ChatBubbleLeftRightIcon,
    ShieldCheckIcon 
} from '@heroicons/react/24/outline';

export default function Welcome({ auth, events, announcements }) {
    const features = [
        {
            name: 'Member Management',
            description: 'Comprehensive member registration, profile management, and admin approval system.',
            icon: UserGroupIcon,
        },
        {
            name: 'Events & Activities',
            description: 'Create, manage, and participate in club events with easy registration and tracking.',
            icon: CalendarDaysIcon,
        },
        {
            name: 'Announcements',
            description: 'Stay updated with important club news, announcements, and communications.',
            icon: SpeakerWaveIcon,
        },
        {
            name: 'Payment System',
            description: 'Secure online payments for membership fees, events, and donations via GCash.',
            icon: CreditCardIcon,
        },
        {
            name: 'Communication Board',
            description: 'Interactive message board for member discussions and community engagement.',
            icon: ChatBubbleLeftRightIcon,
        },
        {
            name: 'Role-Based Access',
            description: 'Secure admin and member roles with appropriate permissions and access controls.',
            icon: ShieldCheckIcon,
        },
    ];

    const stats = [
        { name: 'Active Members', value: '500+' },
        { name: 'Events Hosted', value: '150+' },
        { name: 'Years of Excellence', value: '25+' },
        { name: 'Community Projects', value: '75+' },
    ];

    return (
        <AppLayout title="Welcome to Eagle's Club">
            {/* Hero Section */}
            <div className="relative bg-gradient-to-r from-amber-600 to-orange-600 overflow-hidden">
                <div className="max-w-7xl mx-auto">
                    <div className="relative z-10 pb-8 sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
                        <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
                            <div className="sm:text-center lg:text-left">
                                <h1 className="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl">
                                    <span className="block xl:inline">Welcome to</span>{' '}
                                    <span className="block text-amber-200 xl:inline">Eagle's Club</span>
                                </h1>
                                <p className="mt-3 text-base text-amber-100 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                                    Soar to new heights with us! Join a community of excellence, leadership, and service. 
                                    Experience premium club facilities, exclusive events, and lifelong connections.
                                </p>
                                <div className="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                                    <div className="rounded-md shadow">
                                        <Link
                                            href="/register"
                                            className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-amber-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10 transition-colors"
                                        >
                                            Join the Club
                                        </Link>
                                    </div>
                                    <div className="mt-3 sm:mt-0 sm:ml-3">
                                        <Link
                                            href="/about"
                                            className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-amber-500 hover:bg-amber-400 md:py-4 md:text-lg md:px-10 transition-colors"
                                        >
                                            Learn More
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </main>
                    </div>
                </div>
                <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
                    <div className="h-56 w-full bg-gradient-to-br from-amber-400 to-orange-500 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center">
                        <div className="text-center text-white">
                            <div className="text-8xl mb-4">🦅</div>
                            <h2 className="text-2xl font-bold">Excellence in Flight</h2>
                        </div>
                    </div>
                </div>
            </div>

            {/* Stats Section */}
            <div className="bg-white py-12">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                        {stats.map((stat) => (
                            <div key={stat.name} className="text-center">
                                <div className="text-3xl font-bold text-amber-600">{stat.value}</div>
                                <div className="text-sm text-gray-600">{stat.name}</div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Features Section */}
            <div className="py-12 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="lg:text-center">
                        <h2 className="text-base text-amber-600 font-semibold tracking-wide uppercase">Features</h2>
                        <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                            Everything you need for club management
                        </p>
                        <p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
                            Our comprehensive platform provides all the tools needed for modern club operations.
                        </p>
                    </div>

                    <div className="mt-10">
                        <div className="space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10">
                            {features.map((feature) => (
                                <div key={feature.name} className="relative">
                                    <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-amber-500 text-white">
                                        <feature.icon className="h-6 w-6" aria-hidden="true" />
                                    </div>
                                    <p className="ml-16 text-lg leading-6 font-medium text-gray-900">{feature.name}</p>
                                    <p className="mt-2 ml-16 text-base text-gray-500">{feature.description}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            {/* Recent Events Section */}
            {events && events.length > 0 && (
                <div className="bg-white py-12">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h2 className="text-3xl font-extrabold text-gray-900">Upcoming Events</h2>
                            <p className="mt-4 text-lg text-gray-500">Don't miss out on our exciting upcoming events</p>
                        </div>
                        <div className="mt-10 grid gap-6 lg:grid-cols-3">
                            {events.slice(0, 3).map((event) => (
                                <div key={event.id} className="bg-white overflow-hidden shadow rounded-lg">
                                    <div className="p-6">
                                        <h3 className="text-lg font-medium text-gray-900">{event.title}</h3>
                                        <p className="mt-2 text-sm text-gray-500">{event.description}</p>
                                        <div className="mt-4 flex items-center text-sm text-gray-500">
                                            <CalendarDaysIcon className="h-4 w-4 mr-1" />
                                            {new Date(event.start_date).toLocaleDateString()}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                        <div className="mt-8 text-center">
                            <Link
                                href="/events"
                                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700"
                            >
                                View All Events
                            </Link>
                        </div>
                    </div>
                </div>
            )}

            {/* CTA Section */}
            <div className="bg-amber-600">
                <div className="max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
                    <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
                        <span className="block">Ready to join the Eagle's Club?</span>
                    </h2>
                    <p className="mt-4 text-lg leading-6 text-amber-100">
                        Become part of an exclusive community dedicated to excellence and leadership.
                    </p>
                    <Link
                        href="/register"
                        className="mt-8 w-full inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-amber-600 bg-white hover:bg-amber-50 sm:w-auto"
                    >
                        Apply for Membership
                    </Link>
                </div>
            </div>
        </AppLayout>
    );
}
