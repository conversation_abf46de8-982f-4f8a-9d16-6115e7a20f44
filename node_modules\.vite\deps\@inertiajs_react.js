import {
  axios_default
} from "./chunk-IAPECSFJ.js";
import {
  require_react
} from "./chunk-BQYK6RGN.js";
import {
  __commonJS,
  __export,
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/es-errors/type.js
var require_type = __commonJS({
  "node_modules/es-errors/type.js"(exports, module) {
    "use strict";
    module.exports = TypeError;
  }
});

// (disabled):node_modules/object-inspect/util.inspect
var require_util = __commonJS({
  "(disabled):node_modules/object-inspect/util.inspect"() {
  }
});

// node_modules/object-inspect/index.js
var require_object_inspect = __commonJS({
  "node_modules/object-inspect/index.js"(exports, module) {
    var hasMap = typeof Map === "function" && Map.prototype;
    var mapSizeDescriptor = Object.getOwnPropertyDescriptor && hasMap ? Object.getOwnPropertyDescriptor(Map.prototype, "size") : null;
    var mapSize = hasMap && mapSizeDescriptor && typeof mapSizeDescriptor.get === "function" ? mapSizeDescriptor.get : null;
    var mapForEach = hasMap && Map.prototype.forEach;
    var hasSet = typeof Set === "function" && Set.prototype;
    var setSizeDescriptor = Object.getOwnPropertyDescriptor && hasSet ? Object.getOwnPropertyDescriptor(Set.prototype, "size") : null;
    var setSize = hasSet && setSizeDescriptor && typeof setSizeDescriptor.get === "function" ? setSizeDescriptor.get : null;
    var setForEach = hasSet && Set.prototype.forEach;
    var hasWeakMap = typeof WeakMap === "function" && WeakMap.prototype;
    var weakMapHas = hasWeakMap ? WeakMap.prototype.has : null;
    var hasWeakSet = typeof WeakSet === "function" && WeakSet.prototype;
    var weakSetHas = hasWeakSet ? WeakSet.prototype.has : null;
    var hasWeakRef = typeof WeakRef === "function" && WeakRef.prototype;
    var weakRefDeref = hasWeakRef ? WeakRef.prototype.deref : null;
    var booleanValueOf = Boolean.prototype.valueOf;
    var objectToString = Object.prototype.toString;
    var functionToString2 = Function.prototype.toString;
    var $match = String.prototype.match;
    var $slice = String.prototype.slice;
    var $replace = String.prototype.replace;
    var $toUpperCase = String.prototype.toUpperCase;
    var $toLowerCase = String.prototype.toLowerCase;
    var $test = RegExp.prototype.test;
    var $concat = Array.prototype.concat;
    var $join = Array.prototype.join;
    var $arrSlice = Array.prototype.slice;
    var $floor = Math.floor;
    var bigIntValueOf = typeof BigInt === "function" ? BigInt.prototype.valueOf : null;
    var gOPS = Object.getOwnPropertySymbols;
    var symToString = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? Symbol.prototype.toString : null;
    var hasShammedSymbols = typeof Symbol === "function" && typeof Symbol.iterator === "object";
    var toStringTag = typeof Symbol === "function" && Symbol.toStringTag && (typeof Symbol.toStringTag === hasShammedSymbols ? "object" : "symbol") ? Symbol.toStringTag : null;
    var isEnumerable = Object.prototype.propertyIsEnumerable;
    var gPO = (typeof Reflect === "function" ? Reflect.getPrototypeOf : Object.getPrototypeOf) || ([].__proto__ === Array.prototype ? function(O) {
      return O.__proto__;
    } : null);
    function addNumericSeparator(num, str) {
      if (num === Infinity || num === -Infinity || num !== num || num && num > -1e3 && num < 1e3 || $test.call(/e/, str)) {
        return str;
      }
      var sepRegex = /[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;
      if (typeof num === "number") {
        var int = num < 0 ? -$floor(-num) : $floor(num);
        if (int !== num) {
          var intStr = String(int);
          var dec = $slice.call(str, intStr.length + 1);
          return $replace.call(intStr, sepRegex, "$&_") + "." + $replace.call($replace.call(dec, /([0-9]{3})/g, "$&_"), /_$/, "");
        }
      }
      return $replace.call(str, sepRegex, "$&_");
    }
    var utilInspect = require_util();
    var inspectCustom = utilInspect.custom;
    var inspectSymbol = isSymbol3(inspectCustom) ? inspectCustom : null;
    var quotes = {
      __proto__: null,
      "double": '"',
      single: "'"
    };
    var quoteREs = {
      __proto__: null,
      "double": /(["\\])/g,
      single: /(['\\])/g
    };
    module.exports = function inspect_(obj, options, depth, seen) {
      var opts = options || {};
      if (has2(opts, "quoteStyle") && !has2(quotes, opts.quoteStyle)) {
        throw new TypeError('option "quoteStyle" must be "single" or "double"');
      }
      if (has2(opts, "maxStringLength") && (typeof opts.maxStringLength === "number" ? opts.maxStringLength < 0 && opts.maxStringLength !== Infinity : opts.maxStringLength !== null)) {
        throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');
      }
      var customInspect = has2(opts, "customInspect") ? opts.customInspect : true;
      if (typeof customInspect !== "boolean" && customInspect !== "symbol") {
        throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");
      }
      if (has2(opts, "indent") && opts.indent !== null && opts.indent !== "	" && !(parseInt(opts.indent, 10) === opts.indent && opts.indent > 0)) {
        throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');
      }
      if (has2(opts, "numericSeparator") && typeof opts.numericSeparator !== "boolean") {
        throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');
      }
      var numericSeparator = opts.numericSeparator;
      if (typeof obj === "undefined") {
        return "undefined";
      }
      if (obj === null) {
        return "null";
      }
      if (typeof obj === "boolean") {
        return obj ? "true" : "false";
      }
      if (typeof obj === "string") {
        return inspectString(obj, opts);
      }
      if (typeof obj === "number") {
        if (obj === 0) {
          return Infinity / obj > 0 ? "0" : "-0";
        }
        var str = String(obj);
        return numericSeparator ? addNumericSeparator(obj, str) : str;
      }
      if (typeof obj === "bigint") {
        var bigIntStr = String(obj) + "n";
        return numericSeparator ? addNumericSeparator(obj, bigIntStr) : bigIntStr;
      }
      var maxDepth = typeof opts.depth === "undefined" ? 5 : opts.depth;
      if (typeof depth === "undefined") {
        depth = 0;
      }
      if (depth >= maxDepth && maxDepth > 0 && typeof obj === "object") {
        return isArray2(obj) ? "[Array]" : "[Object]";
      }
      var indent = getIndent(opts, depth);
      if (typeof seen === "undefined") {
        seen = [];
      } else if (indexOf2(seen, obj) >= 0) {
        return "[Circular]";
      }
      function inspect(value, from, noIndent) {
        if (from) {
          seen = $arrSlice.call(seen);
          seen.push(from);
        }
        if (noIndent) {
          var newOpts = {
            depth: opts.depth
          };
          if (has2(opts, "quoteStyle")) {
            newOpts.quoteStyle = opts.quoteStyle;
          }
          return inspect_(value, newOpts, depth + 1, seen);
        }
        return inspect_(value, opts, depth + 1, seen);
      }
      if (typeof obj === "function" && !isRegExp3(obj)) {
        var name = nameOf(obj);
        var keys2 = arrObjKeys(obj, inspect);
        return "[Function" + (name ? ": " + name : " (anonymous)") + "]" + (keys2.length > 0 ? " { " + $join.call(keys2, ", ") + " }" : "");
      }
      if (isSymbol3(obj)) {
        var symString = hasShammedSymbols ? $replace.call(String(obj), /^(Symbol\(.*\))_[^)]*$/, "$1") : symToString.call(obj);
        return typeof obj === "object" && !hasShammedSymbols ? markBoxed(symString) : symString;
      }
      if (isElement2(obj)) {
        var s = "<" + $toLowerCase.call(String(obj.nodeName));
        var attrs = obj.attributes || [];
        for (var i = 0; i < attrs.length; i++) {
          s += " " + attrs[i].name + "=" + wrapQuotes(quote(attrs[i].value), "double", opts);
        }
        s += ">";
        if (obj.childNodes && obj.childNodes.length) {
          s += "...";
        }
        s += "</" + $toLowerCase.call(String(obj.nodeName)) + ">";
        return s;
      }
      if (isArray2(obj)) {
        if (obj.length === 0) {
          return "[]";
        }
        var xs = arrObjKeys(obj, inspect);
        if (indent && !singleLineValues(xs)) {
          return "[" + indentedJoin(xs, indent) + "]";
        }
        return "[ " + $join.call(xs, ", ") + " ]";
      }
      if (isError3(obj)) {
        var parts = arrObjKeys(obj, inspect);
        if (!("cause" in Error.prototype) && "cause" in obj && !isEnumerable.call(obj, "cause")) {
          return "{ [" + String(obj) + "] " + $join.call($concat.call("[cause]: " + inspect(obj.cause), parts), ", ") + " }";
        }
        if (parts.length === 0) {
          return "[" + String(obj) + "]";
        }
        return "{ [" + String(obj) + "] " + $join.call(parts, ", ") + " }";
      }
      if (typeof obj === "object" && customInspect) {
        if (inspectSymbol && typeof obj[inspectSymbol] === "function" && utilInspect) {
          return utilInspect(obj, { depth: maxDepth - depth });
        } else if (customInspect !== "symbol" && typeof obj.inspect === "function") {
          return obj.inspect();
        }
      }
      if (isMap3(obj)) {
        var mapParts = [];
        if (mapForEach) {
          mapForEach.call(obj, function(value, key) {
            mapParts.push(inspect(key, obj, true) + " => " + inspect(value, obj));
          });
        }
        return collectionOf("Map", mapSize.call(obj), mapParts, indent);
      }
      if (isSet3(obj)) {
        var setParts = [];
        if (setForEach) {
          setForEach.call(obj, function(value) {
            setParts.push(inspect(value, obj));
          });
        }
        return collectionOf("Set", setSize.call(obj), setParts, indent);
      }
      if (isWeakMap3(obj)) {
        return weakCollectionOf("WeakMap");
      }
      if (isWeakSet3(obj)) {
        return weakCollectionOf("WeakSet");
      }
      if (isWeakRef(obj)) {
        return weakCollectionOf("WeakRef");
      }
      if (isNumber2(obj)) {
        return markBoxed(inspect(Number(obj)));
      }
      if (isBigInt(obj)) {
        return markBoxed(inspect(bigIntValueOf.call(obj)));
      }
      if (isBoolean3(obj)) {
        return markBoxed(booleanValueOf.call(obj));
      }
      if (isString3(obj)) {
        return markBoxed(inspect(String(obj)));
      }
      if (typeof window !== "undefined" && obj === window) {
        return "{ [object Window] }";
      }
      if (typeof globalThis !== "undefined" && obj === globalThis || typeof global !== "undefined" && obj === global) {
        return "{ [object globalThis] }";
      }
      if (!isDate3(obj) && !isRegExp3(obj)) {
        var ys = arrObjKeys(obj, inspect);
        var isPlainObject3 = gPO ? gPO(obj) === Object.prototype : obj instanceof Object || obj.constructor === Object;
        var protoTag = obj instanceof Object ? "" : "null prototype";
        var stringTag2 = !isPlainObject3 && toStringTag && Object(obj) === obj && toStringTag in obj ? $slice.call(toStr(obj), 8, -1) : protoTag ? "Object" : "";
        var constructorTag = isPlainObject3 || typeof obj.constructor !== "function" ? "" : obj.constructor.name ? obj.constructor.name + " " : "";
        var tag = constructorTag + (stringTag2 || protoTag ? "[" + $join.call($concat.call([], stringTag2 || [], protoTag || []), ": ") + "] " : "");
        if (ys.length === 0) {
          return tag + "{}";
        }
        if (indent) {
          return tag + "{" + indentedJoin(ys, indent) + "}";
        }
        return tag + "{ " + $join.call(ys, ", ") + " }";
      }
      return String(obj);
    };
    function wrapQuotes(s, defaultStyle, opts) {
      var style = opts.quoteStyle || defaultStyle;
      var quoteChar = quotes[style];
      return quoteChar + s + quoteChar;
    }
    function quote(s) {
      return $replace.call(String(s), /"/g, "&quot;");
    }
    function canTrustToString(obj) {
      return !toStringTag || !(typeof obj === "object" && (toStringTag in obj || typeof obj[toStringTag] !== "undefined"));
    }
    function isArray2(obj) {
      return toStr(obj) === "[object Array]" && canTrustToString(obj);
    }
    function isDate3(obj) {
      return toStr(obj) === "[object Date]" && canTrustToString(obj);
    }
    function isRegExp3(obj) {
      return toStr(obj) === "[object RegExp]" && canTrustToString(obj);
    }
    function isError3(obj) {
      return toStr(obj) === "[object Error]" && canTrustToString(obj);
    }
    function isString3(obj) {
      return toStr(obj) === "[object String]" && canTrustToString(obj);
    }
    function isNumber2(obj) {
      return toStr(obj) === "[object Number]" && canTrustToString(obj);
    }
    function isBoolean3(obj) {
      return toStr(obj) === "[object Boolean]" && canTrustToString(obj);
    }
    function isSymbol3(obj) {
      if (hasShammedSymbols) {
        return obj && typeof obj === "object" && obj instanceof Symbol;
      }
      if (typeof obj === "symbol") {
        return true;
      }
      if (!obj || typeof obj !== "object" || !symToString) {
        return false;
      }
      try {
        symToString.call(obj);
        return true;
      } catch (e) {
      }
      return false;
    }
    function isBigInt(obj) {
      if (!obj || typeof obj !== "object" || !bigIntValueOf) {
        return false;
      }
      try {
        bigIntValueOf.call(obj);
        return true;
      } catch (e) {
      }
      return false;
    }
    var hasOwn = Object.prototype.hasOwnProperty || function(key) {
      return key in this;
    };
    function has2(obj, key) {
      return hasOwn.call(obj, key);
    }
    function toStr(obj) {
      return objectToString.call(obj);
    }
    function nameOf(f) {
      if (f.name) {
        return f.name;
      }
      var m = $match.call(functionToString2.call(f), /^function\s*([\w$]+)/);
      if (m) {
        return m[1];
      }
      return null;
    }
    function indexOf2(xs, x) {
      if (xs.indexOf) {
        return xs.indexOf(x);
      }
      for (var i = 0, l = xs.length; i < l; i++) {
        if (xs[i] === x) {
          return i;
        }
      }
      return -1;
    }
    function isMap3(x) {
      if (!mapSize || !x || typeof x !== "object") {
        return false;
      }
      try {
        mapSize.call(x);
        try {
          setSize.call(x);
        } catch (s) {
          return true;
        }
        return x instanceof Map;
      } catch (e) {
      }
      return false;
    }
    function isWeakMap3(x) {
      if (!weakMapHas || !x || typeof x !== "object") {
        return false;
      }
      try {
        weakMapHas.call(x, weakMapHas);
        try {
          weakSetHas.call(x, weakSetHas);
        } catch (s) {
          return true;
        }
        return x instanceof WeakMap;
      } catch (e) {
      }
      return false;
    }
    function isWeakRef(x) {
      if (!weakRefDeref || !x || typeof x !== "object") {
        return false;
      }
      try {
        weakRefDeref.call(x);
        return true;
      } catch (e) {
      }
      return false;
    }
    function isSet3(x) {
      if (!setSize || !x || typeof x !== "object") {
        return false;
      }
      try {
        setSize.call(x);
        try {
          mapSize.call(x);
        } catch (m) {
          return true;
        }
        return x instanceof Set;
      } catch (e) {
      }
      return false;
    }
    function isWeakSet3(x) {
      if (!weakSetHas || !x || typeof x !== "object") {
        return false;
      }
      try {
        weakSetHas.call(x, weakSetHas);
        try {
          weakMapHas.call(x, weakMapHas);
        } catch (s) {
          return true;
        }
        return x instanceof WeakSet;
      } catch (e) {
      }
      return false;
    }
    function isElement2(x) {
      if (!x || typeof x !== "object") {
        return false;
      }
      if (typeof HTMLElement !== "undefined" && x instanceof HTMLElement) {
        return true;
      }
      return typeof x.nodeName === "string" && typeof x.getAttribute === "function";
    }
    function inspectString(str, opts) {
      if (str.length > opts.maxStringLength) {
        var remaining = str.length - opts.maxStringLength;
        var trailer = "... " + remaining + " more character" + (remaining > 1 ? "s" : "");
        return inspectString($slice.call(str, 0, opts.maxStringLength), opts) + trailer;
      }
      var quoteRE = quoteREs[opts.quoteStyle || "single"];
      quoteRE.lastIndex = 0;
      var s = $replace.call($replace.call(str, quoteRE, "\\$1"), /[\x00-\x1f]/g, lowbyte);
      return wrapQuotes(s, "single", opts);
    }
    function lowbyte(c) {
      var n = c.charCodeAt(0);
      var x = {
        8: "b",
        9: "t",
        10: "n",
        12: "f",
        13: "r"
      }[n];
      if (x) {
        return "\\" + x;
      }
      return "\\x" + (n < 16 ? "0" : "") + $toUpperCase.call(n.toString(16));
    }
    function markBoxed(str) {
      return "Object(" + str + ")";
    }
    function weakCollectionOf(type) {
      return type + " { ? }";
    }
    function collectionOf(type, size2, entries, indent) {
      var joinedEntries = indent ? indentedJoin(entries, indent) : $join.call(entries, ", ");
      return type + " (" + size2 + ") {" + joinedEntries + "}";
    }
    function singleLineValues(xs) {
      for (var i = 0; i < xs.length; i++) {
        if (indexOf2(xs[i], "\n") >= 0) {
          return false;
        }
      }
      return true;
    }
    function getIndent(opts, depth) {
      var baseIndent;
      if (opts.indent === "	") {
        baseIndent = "	";
      } else if (typeof opts.indent === "number" && opts.indent > 0) {
        baseIndent = $join.call(Array(opts.indent + 1), " ");
      } else {
        return null;
      }
      return {
        base: baseIndent,
        prev: $join.call(Array(depth + 1), baseIndent)
      };
    }
    function indentedJoin(xs, indent) {
      if (xs.length === 0) {
        return "";
      }
      var lineJoiner = "\n" + indent.prev + indent.base;
      return lineJoiner + $join.call(xs, "," + lineJoiner) + "\n" + indent.prev;
    }
    function arrObjKeys(obj, inspect) {
      var isArr = isArray2(obj);
      var xs = [];
      if (isArr) {
        xs.length = obj.length;
        for (var i = 0; i < obj.length; i++) {
          xs[i] = has2(obj, i) ? inspect(obj[i], obj) : "";
        }
      }
      var syms = typeof gOPS === "function" ? gOPS(obj) : [];
      var symMap;
      if (hasShammedSymbols) {
        symMap = {};
        for (var k = 0; k < syms.length; k++) {
          symMap["$" + syms[k]] = syms[k];
        }
      }
      for (var key in obj) {
        if (!has2(obj, key)) {
          continue;
        }
        if (isArr && String(Number(key)) === key && key < obj.length) {
          continue;
        }
        if (hasShammedSymbols && symMap["$" + key] instanceof Symbol) {
          continue;
        } else if ($test.call(/[^\w$]/, key)) {
          xs.push(inspect(key, obj) + ": " + inspect(obj[key], obj));
        } else {
          xs.push(key + ": " + inspect(obj[key], obj));
        }
      }
      if (typeof gOPS === "function") {
        for (var j = 0; j < syms.length; j++) {
          if (isEnumerable.call(obj, syms[j])) {
            xs.push("[" + inspect(syms[j]) + "]: " + inspect(obj[syms[j]], obj));
          }
        }
      }
      return xs;
    }
  }
});

// node_modules/side-channel-list/index.js
var require_side_channel_list = __commonJS({
  "node_modules/side-channel-list/index.js"(exports, module) {
    "use strict";
    var inspect = require_object_inspect();
    var $TypeError = require_type();
    var listGetNode = function(list, key, isDelete) {
      var prev = list;
      var curr;
      for (; (curr = prev.next) != null; prev = curr) {
        if (curr.key === key) {
          prev.next = curr.next;
          if (!isDelete) {
            curr.next = /** @type {NonNullable<typeof list.next>} */
            list.next;
            list.next = curr;
          }
          return curr;
        }
      }
    };
    var listGet = function(objects, key) {
      if (!objects) {
        return void 0;
      }
      var node = listGetNode(objects, key);
      return node && node.value;
    };
    var listSet = function(objects, key, value) {
      var node = listGetNode(objects, key);
      if (node) {
        node.value = value;
      } else {
        objects.next = /** @type {import('./list.d.ts').ListNode<typeof value, typeof key>} */
        {
          // eslint-disable-line no-param-reassign, no-extra-parens
          key,
          next: objects.next,
          value
        };
      }
    };
    var listHas = function(objects, key) {
      if (!objects) {
        return false;
      }
      return !!listGetNode(objects, key);
    };
    var listDelete = function(objects, key) {
      if (objects) {
        return listGetNode(objects, key, true);
      }
    };
    module.exports = function getSideChannelList() {
      var $o;
      var channel = {
        assert: function(key) {
          if (!channel.has(key)) {
            throw new $TypeError("Side channel does not contain " + inspect(key));
          }
        },
        "delete": function(key) {
          var root = $o && $o.next;
          var deletedNode = listDelete($o, key);
          if (deletedNode && root && root === deletedNode) {
            $o = void 0;
          }
          return !!deletedNode;
        },
        get: function(key) {
          return listGet($o, key);
        },
        has: function(key) {
          return listHas($o, key);
        },
        set: function(key, value) {
          if (!$o) {
            $o = {
              next: void 0
            };
          }
          listSet(
            /** @type {NonNullable<typeof $o>} */
            $o,
            key,
            value
          );
        }
      };
      return channel;
    };
  }
});

// node_modules/es-object-atoms/index.js
var require_es_object_atoms = __commonJS({
  "node_modules/es-object-atoms/index.js"(exports, module) {
    "use strict";
    module.exports = Object;
  }
});

// node_modules/es-errors/index.js
var require_es_errors = __commonJS({
  "node_modules/es-errors/index.js"(exports, module) {
    "use strict";
    module.exports = Error;
  }
});

// node_modules/es-errors/eval.js
var require_eval = __commonJS({
  "node_modules/es-errors/eval.js"(exports, module) {
    "use strict";
    module.exports = EvalError;
  }
});

// node_modules/es-errors/range.js
var require_range = __commonJS({
  "node_modules/es-errors/range.js"(exports, module) {
    "use strict";
    module.exports = RangeError;
  }
});

// node_modules/es-errors/ref.js
var require_ref = __commonJS({
  "node_modules/es-errors/ref.js"(exports, module) {
    "use strict";
    module.exports = ReferenceError;
  }
});

// node_modules/es-errors/syntax.js
var require_syntax = __commonJS({
  "node_modules/es-errors/syntax.js"(exports, module) {
    "use strict";
    module.exports = SyntaxError;
  }
});

// node_modules/es-errors/uri.js
var require_uri = __commonJS({
  "node_modules/es-errors/uri.js"(exports, module) {
    "use strict";
    module.exports = URIError;
  }
});

// node_modules/math-intrinsics/abs.js
var require_abs = __commonJS({
  "node_modules/math-intrinsics/abs.js"(exports, module) {
    "use strict";
    module.exports = Math.abs;
  }
});

// node_modules/math-intrinsics/floor.js
var require_floor = __commonJS({
  "node_modules/math-intrinsics/floor.js"(exports, module) {
    "use strict";
    module.exports = Math.floor;
  }
});

// node_modules/math-intrinsics/max.js
var require_max = __commonJS({
  "node_modules/math-intrinsics/max.js"(exports, module) {
    "use strict";
    module.exports = Math.max;
  }
});

// node_modules/math-intrinsics/min.js
var require_min = __commonJS({
  "node_modules/math-intrinsics/min.js"(exports, module) {
    "use strict";
    module.exports = Math.min;
  }
});

// node_modules/math-intrinsics/pow.js
var require_pow = __commonJS({
  "node_modules/math-intrinsics/pow.js"(exports, module) {
    "use strict";
    module.exports = Math.pow;
  }
});

// node_modules/math-intrinsics/round.js
var require_round = __commonJS({
  "node_modules/math-intrinsics/round.js"(exports, module) {
    "use strict";
    module.exports = Math.round;
  }
});

// node_modules/math-intrinsics/isNaN.js
var require_isNaN = __commonJS({
  "node_modules/math-intrinsics/isNaN.js"(exports, module) {
    "use strict";
    module.exports = Number.isNaN || function isNaN3(a) {
      return a !== a;
    };
  }
});

// node_modules/math-intrinsics/sign.js
var require_sign = __commonJS({
  "node_modules/math-intrinsics/sign.js"(exports, module) {
    "use strict";
    var $isNaN = require_isNaN();
    module.exports = function sign(number) {
      if ($isNaN(number) || number === 0) {
        return number;
      }
      return number < 0 ? -1 : 1;
    };
  }
});

// node_modules/gopd/gOPD.js
var require_gOPD = __commonJS({
  "node_modules/gopd/gOPD.js"(exports, module) {
    "use strict";
    module.exports = Object.getOwnPropertyDescriptor;
  }
});

// node_modules/gopd/index.js
var require_gopd = __commonJS({
  "node_modules/gopd/index.js"(exports, module) {
    "use strict";
    var $gOPD = require_gOPD();
    if ($gOPD) {
      try {
        $gOPD([], "length");
      } catch (e) {
        $gOPD = null;
      }
    }
    module.exports = $gOPD;
  }
});

// node_modules/es-define-property/index.js
var require_es_define_property = __commonJS({
  "node_modules/es-define-property/index.js"(exports, module) {
    "use strict";
    var $defineProperty = Object.defineProperty || false;
    if ($defineProperty) {
      try {
        $defineProperty({}, "a", { value: 1 });
      } catch (e) {
        $defineProperty = false;
      }
    }
    module.exports = $defineProperty;
  }
});

// node_modules/has-symbols/shams.js
var require_shams = __commonJS({
  "node_modules/has-symbols/shams.js"(exports, module) {
    "use strict";
    module.exports = function hasSymbols() {
      if (typeof Symbol !== "function" || typeof Object.getOwnPropertySymbols !== "function") {
        return false;
      }
      if (typeof Symbol.iterator === "symbol") {
        return true;
      }
      var obj = {};
      var sym = Symbol("test");
      var symObj = Object(sym);
      if (typeof sym === "string") {
        return false;
      }
      if (Object.prototype.toString.call(sym) !== "[object Symbol]") {
        return false;
      }
      if (Object.prototype.toString.call(symObj) !== "[object Symbol]") {
        return false;
      }
      var symVal = 42;
      obj[sym] = symVal;
      for (var _ in obj) {
        return false;
      }
      if (typeof Object.keys === "function" && Object.keys(obj).length !== 0) {
        return false;
      }
      if (typeof Object.getOwnPropertyNames === "function" && Object.getOwnPropertyNames(obj).length !== 0) {
        return false;
      }
      var syms = Object.getOwnPropertySymbols(obj);
      if (syms.length !== 1 || syms[0] !== sym) {
        return false;
      }
      if (!Object.prototype.propertyIsEnumerable.call(obj, sym)) {
        return false;
      }
      if (typeof Object.getOwnPropertyDescriptor === "function") {
        var descriptor = (
          /** @type {PropertyDescriptor} */
          Object.getOwnPropertyDescriptor(obj, sym)
        );
        if (descriptor.value !== symVal || descriptor.enumerable !== true) {
          return false;
        }
      }
      return true;
    };
  }
});

// node_modules/has-symbols/index.js
var require_has_symbols = __commonJS({
  "node_modules/has-symbols/index.js"(exports, module) {
    "use strict";
    var origSymbol = typeof Symbol !== "undefined" && Symbol;
    var hasSymbolSham = require_shams();
    module.exports = function hasNativeSymbols() {
      if (typeof origSymbol !== "function") {
        return false;
      }
      if (typeof Symbol !== "function") {
        return false;
      }
      if (typeof origSymbol("foo") !== "symbol") {
        return false;
      }
      if (typeof Symbol("bar") !== "symbol") {
        return false;
      }
      return hasSymbolSham();
    };
  }
});

// node_modules/get-proto/Reflect.getPrototypeOf.js
var require_Reflect_getPrototypeOf = __commonJS({
  "node_modules/get-proto/Reflect.getPrototypeOf.js"(exports, module) {
    "use strict";
    module.exports = typeof Reflect !== "undefined" && Reflect.getPrototypeOf || null;
  }
});

// node_modules/get-proto/Object.getPrototypeOf.js
var require_Object_getPrototypeOf = __commonJS({
  "node_modules/get-proto/Object.getPrototypeOf.js"(exports, module) {
    "use strict";
    var $Object = require_es_object_atoms();
    module.exports = $Object.getPrototypeOf || null;
  }
});

// node_modules/function-bind/implementation.js
var require_implementation = __commonJS({
  "node_modules/function-bind/implementation.js"(exports, module) {
    "use strict";
    var ERROR_MESSAGE = "Function.prototype.bind called on incompatible ";
    var toStr = Object.prototype.toString;
    var max2 = Math.max;
    var funcType = "[object Function]";
    var concatty = function concatty2(a, b) {
      var arr = [];
      for (var i = 0; i < a.length; i += 1) {
        arr[i] = a[i];
      }
      for (var j = 0; j < b.length; j += 1) {
        arr[j + a.length] = b[j];
      }
      return arr;
    };
    var slicy = function slicy2(arrLike, offset) {
      var arr = [];
      for (var i = offset || 0, j = 0; i < arrLike.length; i += 1, j += 1) {
        arr[j] = arrLike[i];
      }
      return arr;
    };
    var joiny = function(arr, joiner) {
      var str = "";
      for (var i = 0; i < arr.length; i += 1) {
        str += arr[i];
        if (i + 1 < arr.length) {
          str += joiner;
        }
      }
      return str;
    };
    module.exports = function bind2(that) {
      var target = this;
      if (typeof target !== "function" || toStr.apply(target) !== funcType) {
        throw new TypeError(ERROR_MESSAGE + target);
      }
      var args = slicy(arguments, 1);
      var bound;
      var binder = function() {
        if (this instanceof bound) {
          var result2 = target.apply(
            this,
            concatty(args, arguments)
          );
          if (Object(result2) === result2) {
            return result2;
          }
          return this;
        }
        return target.apply(
          that,
          concatty(args, arguments)
        );
      };
      var boundLength = max2(0, target.length - args.length);
      var boundArgs = [];
      for (var i = 0; i < boundLength; i++) {
        boundArgs[i] = "$" + i;
      }
      bound = Function("binder", "return function (" + joiny(boundArgs, ",") + "){ return binder.apply(this,arguments); }")(binder);
      if (target.prototype) {
        var Empty = function Empty2() {
        };
        Empty.prototype = target.prototype;
        bound.prototype = new Empty();
        Empty.prototype = null;
      }
      return bound;
    };
  }
});

// node_modules/function-bind/index.js
var require_function_bind = __commonJS({
  "node_modules/function-bind/index.js"(exports, module) {
    "use strict";
    var implementation = require_implementation();
    module.exports = Function.prototype.bind || implementation;
  }
});

// node_modules/call-bind-apply-helpers/functionCall.js
var require_functionCall = __commonJS({
  "node_modules/call-bind-apply-helpers/functionCall.js"(exports, module) {
    "use strict";
    module.exports = Function.prototype.call;
  }
});

// node_modules/call-bind-apply-helpers/functionApply.js
var require_functionApply = __commonJS({
  "node_modules/call-bind-apply-helpers/functionApply.js"(exports, module) {
    "use strict";
    module.exports = Function.prototype.apply;
  }
});

// node_modules/call-bind-apply-helpers/reflectApply.js
var require_reflectApply = __commonJS({
  "node_modules/call-bind-apply-helpers/reflectApply.js"(exports, module) {
    "use strict";
    module.exports = typeof Reflect !== "undefined" && Reflect && Reflect.apply;
  }
});

// node_modules/call-bind-apply-helpers/actualApply.js
var require_actualApply = __commonJS({
  "node_modules/call-bind-apply-helpers/actualApply.js"(exports, module) {
    "use strict";
    var bind2 = require_function_bind();
    var $apply = require_functionApply();
    var $call = require_functionCall();
    var $reflectApply = require_reflectApply();
    module.exports = $reflectApply || bind2.call($call, $apply);
  }
});

// node_modules/call-bind-apply-helpers/index.js
var require_call_bind_apply_helpers = __commonJS({
  "node_modules/call-bind-apply-helpers/index.js"(exports, module) {
    "use strict";
    var bind2 = require_function_bind();
    var $TypeError = require_type();
    var $call = require_functionCall();
    var $actualApply = require_actualApply();
    module.exports = function callBindBasic(args) {
      if (args.length < 1 || typeof args[0] !== "function") {
        throw new $TypeError("a function is required");
      }
      return $actualApply(bind2, $call, args);
    };
  }
});

// node_modules/dunder-proto/get.js
var require_get = __commonJS({
  "node_modules/dunder-proto/get.js"(exports, module) {
    "use strict";
    var callBind = require_call_bind_apply_helpers();
    var gOPD = require_gopd();
    var hasProtoAccessor;
    try {
      hasProtoAccessor = /** @type {{ __proto__?: typeof Array.prototype }} */
      [].__proto__ === Array.prototype;
    } catch (e) {
      if (!e || typeof e !== "object" || !("code" in e) || e.code !== "ERR_PROTO_ACCESS") {
        throw e;
      }
    }
    var desc = !!hasProtoAccessor && gOPD && gOPD(
      Object.prototype,
      /** @type {keyof typeof Object.prototype} */
      "__proto__"
    );
    var $Object = Object;
    var $getPrototypeOf = $Object.getPrototypeOf;
    module.exports = desc && typeof desc.get === "function" ? callBind([desc.get]) : typeof $getPrototypeOf === "function" ? (
      /** @type {import('./get')} */
      function getDunder(value) {
        return $getPrototypeOf(value == null ? value : $Object(value));
      }
    ) : false;
  }
});

// node_modules/get-proto/index.js
var require_get_proto = __commonJS({
  "node_modules/get-proto/index.js"(exports, module) {
    "use strict";
    var reflectGetProto = require_Reflect_getPrototypeOf();
    var originalGetProto = require_Object_getPrototypeOf();
    var getDunderProto = require_get();
    module.exports = reflectGetProto ? function getProto(O) {
      return reflectGetProto(O);
    } : originalGetProto ? function getProto(O) {
      if (!O || typeof O !== "object" && typeof O !== "function") {
        throw new TypeError("getProto: not an object");
      }
      return originalGetProto(O);
    } : getDunderProto ? function getProto(O) {
      return getDunderProto(O);
    } : null;
  }
});

// node_modules/hasown/index.js
var require_hasown = __commonJS({
  "node_modules/hasown/index.js"(exports, module) {
    "use strict";
    var call = Function.prototype.call;
    var $hasOwn = Object.prototype.hasOwnProperty;
    var bind2 = require_function_bind();
    module.exports = bind2.call(call, $hasOwn);
  }
});

// node_modules/get-intrinsic/index.js
var require_get_intrinsic = __commonJS({
  "node_modules/get-intrinsic/index.js"(exports, module) {
    "use strict";
    var undefined2;
    var $Object = require_es_object_atoms();
    var $Error = require_es_errors();
    var $EvalError = require_eval();
    var $RangeError = require_range();
    var $ReferenceError = require_ref();
    var $SyntaxError = require_syntax();
    var $TypeError = require_type();
    var $URIError = require_uri();
    var abs = require_abs();
    var floor2 = require_floor();
    var max2 = require_max();
    var min2 = require_min();
    var pow = require_pow();
    var round3 = require_round();
    var sign = require_sign();
    var $Function = Function;
    var getEvalledConstructor = function(expressionSyntax) {
      try {
        return $Function('"use strict"; return (' + expressionSyntax + ").constructor;")();
      } catch (e) {
      }
    };
    var $gOPD = require_gopd();
    var $defineProperty = require_es_define_property();
    var throwTypeError = function() {
      throw new $TypeError();
    };
    var ThrowTypeError = $gOPD ? function() {
      try {
        arguments.callee;
        return throwTypeError;
      } catch (calleeThrows) {
        try {
          return $gOPD(arguments, "callee").get;
        } catch (gOPDthrows) {
          return throwTypeError;
        }
      }
    }() : throwTypeError;
    var hasSymbols = require_has_symbols()();
    var getProto = require_get_proto();
    var $ObjectGPO = require_Object_getPrototypeOf();
    var $ReflectGPO = require_Reflect_getPrototypeOf();
    var $apply = require_functionApply();
    var $call = require_functionCall();
    var needsEval = {};
    var TypedArray = typeof Uint8Array === "undefined" || !getProto ? undefined2 : getProto(Uint8Array);
    var INTRINSICS = {
      __proto__: null,
      "%AggregateError%": typeof AggregateError === "undefined" ? undefined2 : AggregateError,
      "%Array%": Array,
      "%ArrayBuffer%": typeof ArrayBuffer === "undefined" ? undefined2 : ArrayBuffer,
      "%ArrayIteratorPrototype%": hasSymbols && getProto ? getProto([][Symbol.iterator]()) : undefined2,
      "%AsyncFromSyncIteratorPrototype%": undefined2,
      "%AsyncFunction%": needsEval,
      "%AsyncGenerator%": needsEval,
      "%AsyncGeneratorFunction%": needsEval,
      "%AsyncIteratorPrototype%": needsEval,
      "%Atomics%": typeof Atomics === "undefined" ? undefined2 : Atomics,
      "%BigInt%": typeof BigInt === "undefined" ? undefined2 : BigInt,
      "%BigInt64Array%": typeof BigInt64Array === "undefined" ? undefined2 : BigInt64Array,
      "%BigUint64Array%": typeof BigUint64Array === "undefined" ? undefined2 : BigUint64Array,
      "%Boolean%": Boolean,
      "%DataView%": typeof DataView === "undefined" ? undefined2 : DataView,
      "%Date%": Date,
      "%decodeURI%": decodeURI,
      "%decodeURIComponent%": decodeURIComponent,
      "%encodeURI%": encodeURI,
      "%encodeURIComponent%": encodeURIComponent,
      "%Error%": $Error,
      "%eval%": eval,
      // eslint-disable-line no-eval
      "%EvalError%": $EvalError,
      "%Float16Array%": typeof Float16Array === "undefined" ? undefined2 : Float16Array,
      "%Float32Array%": typeof Float32Array === "undefined" ? undefined2 : Float32Array,
      "%Float64Array%": typeof Float64Array === "undefined" ? undefined2 : Float64Array,
      "%FinalizationRegistry%": typeof FinalizationRegistry === "undefined" ? undefined2 : FinalizationRegistry,
      "%Function%": $Function,
      "%GeneratorFunction%": needsEval,
      "%Int8Array%": typeof Int8Array === "undefined" ? undefined2 : Int8Array,
      "%Int16Array%": typeof Int16Array === "undefined" ? undefined2 : Int16Array,
      "%Int32Array%": typeof Int32Array === "undefined" ? undefined2 : Int32Array,
      "%isFinite%": isFinite,
      "%isNaN%": isNaN,
      "%IteratorPrototype%": hasSymbols && getProto ? getProto(getProto([][Symbol.iterator]())) : undefined2,
      "%JSON%": typeof JSON === "object" ? JSON : undefined2,
      "%Map%": typeof Map === "undefined" ? undefined2 : Map,
      "%MapIteratorPrototype%": typeof Map === "undefined" || !hasSymbols || !getProto ? undefined2 : getProto((/* @__PURE__ */ new Map())[Symbol.iterator]()),
      "%Math%": Math,
      "%Number%": Number,
      "%Object%": $Object,
      "%Object.getOwnPropertyDescriptor%": $gOPD,
      "%parseFloat%": parseFloat,
      "%parseInt%": parseInt,
      "%Promise%": typeof Promise === "undefined" ? undefined2 : Promise,
      "%Proxy%": typeof Proxy === "undefined" ? undefined2 : Proxy,
      "%RangeError%": $RangeError,
      "%ReferenceError%": $ReferenceError,
      "%Reflect%": typeof Reflect === "undefined" ? undefined2 : Reflect,
      "%RegExp%": RegExp,
      "%Set%": typeof Set === "undefined" ? undefined2 : Set,
      "%SetIteratorPrototype%": typeof Set === "undefined" || !hasSymbols || !getProto ? undefined2 : getProto((/* @__PURE__ */ new Set())[Symbol.iterator]()),
      "%SharedArrayBuffer%": typeof SharedArrayBuffer === "undefined" ? undefined2 : SharedArrayBuffer,
      "%String%": String,
      "%StringIteratorPrototype%": hasSymbols && getProto ? getProto(""[Symbol.iterator]()) : undefined2,
      "%Symbol%": hasSymbols ? Symbol : undefined2,
      "%SyntaxError%": $SyntaxError,
      "%ThrowTypeError%": ThrowTypeError,
      "%TypedArray%": TypedArray,
      "%TypeError%": $TypeError,
      "%Uint8Array%": typeof Uint8Array === "undefined" ? undefined2 : Uint8Array,
      "%Uint8ClampedArray%": typeof Uint8ClampedArray === "undefined" ? undefined2 : Uint8ClampedArray,
      "%Uint16Array%": typeof Uint16Array === "undefined" ? undefined2 : Uint16Array,
      "%Uint32Array%": typeof Uint32Array === "undefined" ? undefined2 : Uint32Array,
      "%URIError%": $URIError,
      "%WeakMap%": typeof WeakMap === "undefined" ? undefined2 : WeakMap,
      "%WeakRef%": typeof WeakRef === "undefined" ? undefined2 : WeakRef,
      "%WeakSet%": typeof WeakSet === "undefined" ? undefined2 : WeakSet,
      "%Function.prototype.call%": $call,
      "%Function.prototype.apply%": $apply,
      "%Object.defineProperty%": $defineProperty,
      "%Object.getPrototypeOf%": $ObjectGPO,
      "%Math.abs%": abs,
      "%Math.floor%": floor2,
      "%Math.max%": max2,
      "%Math.min%": min2,
      "%Math.pow%": pow,
      "%Math.round%": round3,
      "%Math.sign%": sign,
      "%Reflect.getPrototypeOf%": $ReflectGPO
    };
    if (getProto) {
      try {
        null.error;
      } catch (e) {
        errorProto = getProto(getProto(e));
        INTRINSICS["%Error.prototype%"] = errorProto;
      }
    }
    var errorProto;
    var doEval = function doEval2(name) {
      var value;
      if (name === "%AsyncFunction%") {
        value = getEvalledConstructor("async function () {}");
      } else if (name === "%GeneratorFunction%") {
        value = getEvalledConstructor("function* () {}");
      } else if (name === "%AsyncGeneratorFunction%") {
        value = getEvalledConstructor("async function* () {}");
      } else if (name === "%AsyncGenerator%") {
        var fn = doEval2("%AsyncGeneratorFunction%");
        if (fn) {
          value = fn.prototype;
        }
      } else if (name === "%AsyncIteratorPrototype%") {
        var gen = doEval2("%AsyncGenerator%");
        if (gen && getProto) {
          value = getProto(gen.prototype);
        }
      }
      INTRINSICS[name] = value;
      return value;
    };
    var LEGACY_ALIASES = {
      __proto__: null,
      "%ArrayBufferPrototype%": ["ArrayBuffer", "prototype"],
      "%ArrayPrototype%": ["Array", "prototype"],
      "%ArrayProto_entries%": ["Array", "prototype", "entries"],
      "%ArrayProto_forEach%": ["Array", "prototype", "forEach"],
      "%ArrayProto_keys%": ["Array", "prototype", "keys"],
      "%ArrayProto_values%": ["Array", "prototype", "values"],
      "%AsyncFunctionPrototype%": ["AsyncFunction", "prototype"],
      "%AsyncGenerator%": ["AsyncGeneratorFunction", "prototype"],
      "%AsyncGeneratorPrototype%": ["AsyncGeneratorFunction", "prototype", "prototype"],
      "%BooleanPrototype%": ["Boolean", "prototype"],
      "%DataViewPrototype%": ["DataView", "prototype"],
      "%DatePrototype%": ["Date", "prototype"],
      "%ErrorPrototype%": ["Error", "prototype"],
      "%EvalErrorPrototype%": ["EvalError", "prototype"],
      "%Float32ArrayPrototype%": ["Float32Array", "prototype"],
      "%Float64ArrayPrototype%": ["Float64Array", "prototype"],
      "%FunctionPrototype%": ["Function", "prototype"],
      "%Generator%": ["GeneratorFunction", "prototype"],
      "%GeneratorPrototype%": ["GeneratorFunction", "prototype", "prototype"],
      "%Int8ArrayPrototype%": ["Int8Array", "prototype"],
      "%Int16ArrayPrototype%": ["Int16Array", "prototype"],
      "%Int32ArrayPrototype%": ["Int32Array", "prototype"],
      "%JSONParse%": ["JSON", "parse"],
      "%JSONStringify%": ["JSON", "stringify"],
      "%MapPrototype%": ["Map", "prototype"],
      "%NumberPrototype%": ["Number", "prototype"],
      "%ObjectPrototype%": ["Object", "prototype"],
      "%ObjProto_toString%": ["Object", "prototype", "toString"],
      "%ObjProto_valueOf%": ["Object", "prototype", "valueOf"],
      "%PromisePrototype%": ["Promise", "prototype"],
      "%PromiseProto_then%": ["Promise", "prototype", "then"],
      "%Promise_all%": ["Promise", "all"],
      "%Promise_reject%": ["Promise", "reject"],
      "%Promise_resolve%": ["Promise", "resolve"],
      "%RangeErrorPrototype%": ["RangeError", "prototype"],
      "%ReferenceErrorPrototype%": ["ReferenceError", "prototype"],
      "%RegExpPrototype%": ["RegExp", "prototype"],
      "%SetPrototype%": ["Set", "prototype"],
      "%SharedArrayBufferPrototype%": ["SharedArrayBuffer", "prototype"],
      "%StringPrototype%": ["String", "prototype"],
      "%SymbolPrototype%": ["Symbol", "prototype"],
      "%SyntaxErrorPrototype%": ["SyntaxError", "prototype"],
      "%TypedArrayPrototype%": ["TypedArray", "prototype"],
      "%TypeErrorPrototype%": ["TypeError", "prototype"],
      "%Uint8ArrayPrototype%": ["Uint8Array", "prototype"],
      "%Uint8ClampedArrayPrototype%": ["Uint8ClampedArray", "prototype"],
      "%Uint16ArrayPrototype%": ["Uint16Array", "prototype"],
      "%Uint32ArrayPrototype%": ["Uint32Array", "prototype"],
      "%URIErrorPrototype%": ["URIError", "prototype"],
      "%WeakMapPrototype%": ["WeakMap", "prototype"],
      "%WeakSetPrototype%": ["WeakSet", "prototype"]
    };
    var bind2 = require_function_bind();
    var hasOwn = require_hasown();
    var $concat = bind2.call($call, Array.prototype.concat);
    var $spliceApply = bind2.call($apply, Array.prototype.splice);
    var $replace = bind2.call($call, String.prototype.replace);
    var $strSlice = bind2.call($call, String.prototype.slice);
    var $exec = bind2.call($call, RegExp.prototype.exec);
    var rePropName = /[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g;
    var reEscapeChar = /\\(\\)?/g;
    var stringToPath = function stringToPath2(string) {
      var first = $strSlice(string, 0, 1);
      var last3 = $strSlice(string, -1);
      if (first === "%" && last3 !== "%") {
        throw new $SyntaxError("invalid intrinsic syntax, expected closing `%`");
      } else if (last3 === "%" && first !== "%") {
        throw new $SyntaxError("invalid intrinsic syntax, expected opening `%`");
      }
      var result2 = [];
      $replace(string, rePropName, function(match, number, quote, subString) {
        result2[result2.length] = quote ? $replace(subString, reEscapeChar, "$1") : number || match;
      });
      return result2;
    };
    var getBaseIntrinsic = function getBaseIntrinsic2(name, allowMissing) {
      var intrinsicName = name;
      var alias;
      if (hasOwn(LEGACY_ALIASES, intrinsicName)) {
        alias = LEGACY_ALIASES[intrinsicName];
        intrinsicName = "%" + alias[0] + "%";
      }
      if (hasOwn(INTRINSICS, intrinsicName)) {
        var value = INTRINSICS[intrinsicName];
        if (value === needsEval) {
          value = doEval(intrinsicName);
        }
        if (typeof value === "undefined" && !allowMissing) {
          throw new $TypeError("intrinsic " + name + " exists, but is not available. Please file an issue!");
        }
        return {
          alias,
          name: intrinsicName,
          value
        };
      }
      throw new $SyntaxError("intrinsic " + name + " does not exist!");
    };
    module.exports = function GetIntrinsic(name, allowMissing) {
      if (typeof name !== "string" || name.length === 0) {
        throw new $TypeError("intrinsic name must be a non-empty string");
      }
      if (arguments.length > 1 && typeof allowMissing !== "boolean") {
        throw new $TypeError('"allowMissing" argument must be a boolean');
      }
      if ($exec(/^%?[^%]*%?$/, name) === null) {
        throw new $SyntaxError("`%` may not be present anywhere but at the beginning and end of the intrinsic name");
      }
      var parts = stringToPath(name);
      var intrinsicBaseName = parts.length > 0 ? parts[0] : "";
      var intrinsic = getBaseIntrinsic("%" + intrinsicBaseName + "%", allowMissing);
      var intrinsicRealName = intrinsic.name;
      var value = intrinsic.value;
      var skipFurtherCaching = false;
      var alias = intrinsic.alias;
      if (alias) {
        intrinsicBaseName = alias[0];
        $spliceApply(parts, $concat([0, 1], alias));
      }
      for (var i = 1, isOwn = true; i < parts.length; i += 1) {
        var part = parts[i];
        var first = $strSlice(part, 0, 1);
        var last3 = $strSlice(part, -1);
        if ((first === '"' || first === "'" || first === "`" || (last3 === '"' || last3 === "'" || last3 === "`")) && first !== last3) {
          throw new $SyntaxError("property names with quotes must have matching quotes");
        }
        if (part === "constructor" || !isOwn) {
          skipFurtherCaching = true;
        }
        intrinsicBaseName += "." + part;
        intrinsicRealName = "%" + intrinsicBaseName + "%";
        if (hasOwn(INTRINSICS, intrinsicRealName)) {
          value = INTRINSICS[intrinsicRealName];
        } else if (value != null) {
          if (!(part in value)) {
            if (!allowMissing) {
              throw new $TypeError("base intrinsic for " + name + " exists, but the property is not available.");
            }
            return void undefined2;
          }
          if ($gOPD && i + 1 >= parts.length) {
            var desc = $gOPD(value, part);
            isOwn = !!desc;
            if (isOwn && "get" in desc && !("originalValue" in desc.get)) {
              value = desc.get;
            } else {
              value = value[part];
            }
          } else {
            isOwn = hasOwn(value, part);
            value = value[part];
          }
          if (isOwn && !skipFurtherCaching) {
            INTRINSICS[intrinsicRealName] = value;
          }
        }
      }
      return value;
    };
  }
});

// node_modules/call-bound/index.js
var require_call_bound = __commonJS({
  "node_modules/call-bound/index.js"(exports, module) {
    "use strict";
    var GetIntrinsic = require_get_intrinsic();
    var callBindBasic = require_call_bind_apply_helpers();
    var $indexOf = callBindBasic([GetIntrinsic("%String.prototype.indexOf%")]);
    module.exports = function callBoundIntrinsic(name, allowMissing) {
      var intrinsic = (
        /** @type {(this: unknown, ...args: unknown[]) => unknown} */
        GetIntrinsic(name, !!allowMissing)
      );
      if (typeof intrinsic === "function" && $indexOf(name, ".prototype.") > -1) {
        return callBindBasic(
          /** @type {const} */
          [intrinsic]
        );
      }
      return intrinsic;
    };
  }
});

// node_modules/side-channel-map/index.js
var require_side_channel_map = __commonJS({
  "node_modules/side-channel-map/index.js"(exports, module) {
    "use strict";
    var GetIntrinsic = require_get_intrinsic();
    var callBound = require_call_bound();
    var inspect = require_object_inspect();
    var $TypeError = require_type();
    var $Map = GetIntrinsic("%Map%", true);
    var $mapGet = callBound("Map.prototype.get", true);
    var $mapSet = callBound("Map.prototype.set", true);
    var $mapHas = callBound("Map.prototype.has", true);
    var $mapDelete = callBound("Map.prototype.delete", true);
    var $mapSize = callBound("Map.prototype.size", true);
    module.exports = !!$Map && /** @type {Exclude<import('.'), false>} */
    function getSideChannelMap() {
      var $m;
      var channel = {
        assert: function(key) {
          if (!channel.has(key)) {
            throw new $TypeError("Side channel does not contain " + inspect(key));
          }
        },
        "delete": function(key) {
          if ($m) {
            var result2 = $mapDelete($m, key);
            if ($mapSize($m) === 0) {
              $m = void 0;
            }
            return result2;
          }
          return false;
        },
        get: function(key) {
          if ($m) {
            return $mapGet($m, key);
          }
        },
        has: function(key) {
          if ($m) {
            return $mapHas($m, key);
          }
          return false;
        },
        set: function(key, value) {
          if (!$m) {
            $m = new $Map();
          }
          $mapSet($m, key, value);
        }
      };
      return channel;
    };
  }
});

// node_modules/side-channel-weakmap/index.js
var require_side_channel_weakmap = __commonJS({
  "node_modules/side-channel-weakmap/index.js"(exports, module) {
    "use strict";
    var GetIntrinsic = require_get_intrinsic();
    var callBound = require_call_bound();
    var inspect = require_object_inspect();
    var getSideChannelMap = require_side_channel_map();
    var $TypeError = require_type();
    var $WeakMap = GetIntrinsic("%WeakMap%", true);
    var $weakMapGet = callBound("WeakMap.prototype.get", true);
    var $weakMapSet = callBound("WeakMap.prototype.set", true);
    var $weakMapHas = callBound("WeakMap.prototype.has", true);
    var $weakMapDelete = callBound("WeakMap.prototype.delete", true);
    module.exports = $WeakMap ? (
      /** @type {Exclude<import('.'), false>} */
      function getSideChannelWeakMap() {
        var $wm;
        var $m;
        var channel = {
          assert: function(key) {
            if (!channel.has(key)) {
              throw new $TypeError("Side channel does not contain " + inspect(key));
            }
          },
          "delete": function(key) {
            if ($WeakMap && key && (typeof key === "object" || typeof key === "function")) {
              if ($wm) {
                return $weakMapDelete($wm, key);
              }
            } else if (getSideChannelMap) {
              if ($m) {
                return $m["delete"](key);
              }
            }
            return false;
          },
          get: function(key) {
            if ($WeakMap && key && (typeof key === "object" || typeof key === "function")) {
              if ($wm) {
                return $weakMapGet($wm, key);
              }
            }
            return $m && $m.get(key);
          },
          has: function(key) {
            if ($WeakMap && key && (typeof key === "object" || typeof key === "function")) {
              if ($wm) {
                return $weakMapHas($wm, key);
              }
            }
            return !!$m && $m.has(key);
          },
          set: function(key, value) {
            if ($WeakMap && key && (typeof key === "object" || typeof key === "function")) {
              if (!$wm) {
                $wm = new $WeakMap();
              }
              $weakMapSet($wm, key, value);
            } else if (getSideChannelMap) {
              if (!$m) {
                $m = getSideChannelMap();
              }
              $m.set(key, value);
            }
          }
        };
        return channel;
      }
    ) : getSideChannelMap;
  }
});

// node_modules/side-channel/index.js
var require_side_channel = __commonJS({
  "node_modules/side-channel/index.js"(exports, module) {
    "use strict";
    var $TypeError = require_type();
    var inspect = require_object_inspect();
    var getSideChannelList = require_side_channel_list();
    var getSideChannelMap = require_side_channel_map();
    var getSideChannelWeakMap = require_side_channel_weakmap();
    var makeChannel = getSideChannelWeakMap || getSideChannelMap || getSideChannelList;
    module.exports = function getSideChannel() {
      var $channelData;
      var channel = {
        assert: function(key) {
          if (!channel.has(key)) {
            throw new $TypeError("Side channel does not contain " + inspect(key));
          }
        },
        "delete": function(key) {
          return !!$channelData && $channelData["delete"](key);
        },
        get: function(key) {
          return $channelData && $channelData.get(key);
        },
        has: function(key) {
          return !!$channelData && $channelData.has(key);
        },
        set: function(key, value) {
          if (!$channelData) {
            $channelData = makeChannel();
          }
          $channelData.set(key, value);
        }
      };
      return channel;
    };
  }
});

// node_modules/qs/lib/formats.js
var require_formats = __commonJS({
  "node_modules/qs/lib/formats.js"(exports, module) {
    "use strict";
    var replace2 = String.prototype.replace;
    var percentTwenties = /%20/g;
    var Format = {
      RFC1738: "RFC1738",
      RFC3986: "RFC3986"
    };
    module.exports = {
      "default": Format.RFC3986,
      formatters: {
        RFC1738: function(value) {
          return replace2.call(value, percentTwenties, "+");
        },
        RFC3986: function(value) {
          return String(value);
        }
      },
      RFC1738: Format.RFC1738,
      RFC3986: Format.RFC3986
    };
  }
});

// node_modules/qs/lib/utils.js
var require_utils = __commonJS({
  "node_modules/qs/lib/utils.js"(exports, module) {
    "use strict";
    var formats = require_formats();
    var has2 = Object.prototype.hasOwnProperty;
    var isArray2 = Array.isArray;
    var hexTable = function() {
      var array = [];
      for (var i = 0; i < 256; ++i) {
        array.push("%" + ((i < 16 ? "0" : "") + i.toString(16)).toUpperCase());
      }
      return array;
    }();
    var compactQueue = function compactQueue2(queue4) {
      while (queue4.length > 1) {
        var item = queue4.pop();
        var obj = item.obj[item.prop];
        if (isArray2(obj)) {
          var compacted = [];
          for (var j = 0; j < obj.length; ++j) {
            if (typeof obj[j] !== "undefined") {
              compacted.push(obj[j]);
            }
          }
          item.obj[item.prop] = compacted;
        }
      }
    };
    var arrayToObject = function arrayToObject2(source, options) {
      var obj = options && options.plainObjects ? { __proto__: null } : {};
      for (var i = 0; i < source.length; ++i) {
        if (typeof source[i] !== "undefined") {
          obj[i] = source[i];
        }
      }
      return obj;
    };
    var merge3 = function merge4(target, source, options) {
      if (!source) {
        return target;
      }
      if (typeof source !== "object" && typeof source !== "function") {
        if (isArray2(target)) {
          target.push(source);
        } else if (target && typeof target === "object") {
          if (options && (options.plainObjects || options.allowPrototypes) || !has2.call(Object.prototype, source)) {
            target[source] = true;
          }
        } else {
          return [target, source];
        }
        return target;
      }
      if (!target || typeof target !== "object") {
        return [target].concat(source);
      }
      var mergeTarget = target;
      if (isArray2(target) && !isArray2(source)) {
        mergeTarget = arrayToObject(target, options);
      }
      if (isArray2(target) && isArray2(source)) {
        source.forEach(function(item, i) {
          if (has2.call(target, i)) {
            var targetItem = target[i];
            if (targetItem && typeof targetItem === "object" && item && typeof item === "object") {
              target[i] = merge4(targetItem, item, options);
            } else {
              target.push(item);
            }
          } else {
            target[i] = item;
          }
        });
        return target;
      }
      return Object.keys(source).reduce(function(acc, key) {
        var value = source[key];
        if (has2.call(acc, key)) {
          acc[key] = merge4(acc[key], value, options);
        } else {
          acc[key] = value;
        }
        return acc;
      }, mergeTarget);
    };
    var assign2 = function assignSingleSource(target, source) {
      return Object.keys(source).reduce(function(acc, key) {
        acc[key] = source[key];
        return acc;
      }, target);
    };
    var decode = function(str, defaultDecoder, charset) {
      var strWithoutPlus = str.replace(/\+/g, " ");
      if (charset === "iso-8859-1") {
        return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);
      }
      try {
        return decodeURIComponent(strWithoutPlus);
      } catch (e) {
        return strWithoutPlus;
      }
    };
    var limit = 1024;
    var encode = function encode2(str, defaultEncoder, charset, kind, format) {
      if (str.length === 0) {
        return str;
      }
      var string = str;
      if (typeof str === "symbol") {
        string = Symbol.prototype.toString.call(str);
      } else if (typeof str !== "string") {
        string = String(str);
      }
      if (charset === "iso-8859-1") {
        return escape(string).replace(/%u[0-9a-f]{4}/gi, function($0) {
          return "%26%23" + parseInt($0.slice(2), 16) + "%3B";
        });
      }
      var out = "";
      for (var j = 0; j < string.length; j += limit) {
        var segment = string.length >= limit ? string.slice(j, j + limit) : string;
        var arr = [];
        for (var i = 0; i < segment.length; ++i) {
          var c = segment.charCodeAt(i);
          if (c === 45 || c === 46 || c === 95 || c === 126 || c >= 48 && c <= 57 || c >= 65 && c <= 90 || c >= 97 && c <= 122 || format === formats.RFC1738 && (c === 40 || c === 41)) {
            arr[arr.length] = segment.charAt(i);
            continue;
          }
          if (c < 128) {
            arr[arr.length] = hexTable[c];
            continue;
          }
          if (c < 2048) {
            arr[arr.length] = hexTable[192 | c >> 6] + hexTable[128 | c & 63];
            continue;
          }
          if (c < 55296 || c >= 57344) {
            arr[arr.length] = hexTable[224 | c >> 12] + hexTable[128 | c >> 6 & 63] + hexTable[128 | c & 63];
            continue;
          }
          i += 1;
          c = 65536 + ((c & 1023) << 10 | segment.charCodeAt(i) & 1023);
          arr[arr.length] = hexTable[240 | c >> 18] + hexTable[128 | c >> 12 & 63] + hexTable[128 | c >> 6 & 63] + hexTable[128 | c & 63];
        }
        out += arr.join("");
      }
      return out;
    };
    var compact3 = function compact4(value) {
      var queue4 = [{ obj: { o: value }, prop: "o" }];
      var refs = [];
      for (var i = 0; i < queue4.length; ++i) {
        var item = queue4[i];
        var obj = item.obj[item.prop];
        var keys2 = Object.keys(obj);
        for (var j = 0; j < keys2.length; ++j) {
          var key = keys2[j];
          var val = obj[key];
          if (typeof val === "object" && val !== null && refs.indexOf(val) === -1) {
            queue4.push({ obj, prop: key });
            refs.push(val);
          }
        }
      }
      compactQueue(queue4);
      return value;
    };
    var isRegExp3 = function isRegExp4(obj) {
      return Object.prototype.toString.call(obj) === "[object RegExp]";
    };
    var isBuffer3 = function isBuffer4(obj) {
      if (!obj || typeof obj !== "object") {
        return false;
      }
      return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));
    };
    var combine = function combine2(a, b) {
      return [].concat(a, b);
    };
    var maybeMap = function maybeMap2(val, fn) {
      if (isArray2(val)) {
        var mapped = [];
        for (var i = 0; i < val.length; i += 1) {
          mapped.push(fn(val[i]));
        }
        return mapped;
      }
      return fn(val);
    };
    module.exports = {
      arrayToObject,
      assign: assign2,
      combine,
      compact: compact3,
      decode,
      encode,
      isBuffer: isBuffer3,
      isRegExp: isRegExp3,
      maybeMap,
      merge: merge3
    };
  }
});

// node_modules/qs/lib/stringify.js
var require_stringify = __commonJS({
  "node_modules/qs/lib/stringify.js"(exports, module) {
    "use strict";
    var getSideChannel = require_side_channel();
    var utils = require_utils();
    var formats = require_formats();
    var has2 = Object.prototype.hasOwnProperty;
    var arrayPrefixGenerators = {
      brackets: function brackets(prefix) {
        return prefix + "[]";
      },
      comma: "comma",
      indices: function indices(prefix, key) {
        return prefix + "[" + key + "]";
      },
      repeat: function repeat2(prefix) {
        return prefix;
      }
    };
    var isArray2 = Array.isArray;
    var push = Array.prototype.push;
    var pushToArray = function(arr, valueOrArray) {
      push.apply(arr, isArray2(valueOrArray) ? valueOrArray : [valueOrArray]);
    };
    var toISO = Date.prototype.toISOString;
    var defaultFormat = formats["default"];
    var defaults2 = {
      addQueryPrefix: false,
      allowDots: false,
      allowEmptyArrays: false,
      arrayFormat: "indices",
      charset: "utf-8",
      charsetSentinel: false,
      commaRoundTrip: false,
      delimiter: "&",
      encode: true,
      encodeDotInKeys: false,
      encoder: utils.encode,
      encodeValuesOnly: false,
      filter: void 0,
      format: defaultFormat,
      formatter: formats.formatters[defaultFormat],
      // deprecated
      indices: false,
      serializeDate: function serializeDate(date) {
        return toISO.call(date);
      },
      skipNulls: false,
      strictNullHandling: false
    };
    var isNonNullishPrimitive = function isNonNullishPrimitive2(v) {
      return typeof v === "string" || typeof v === "number" || typeof v === "boolean" || typeof v === "symbol" || typeof v === "bigint";
    };
    var sentinel = {};
    var stringify2 = function stringify3(object, prefix, generateArrayPrefix, commaRoundTrip, allowEmptyArrays, strictNullHandling, skipNulls, encodeDotInKeys, encoder, filter2, sort, allowDots, serializeDate, format, formatter, encodeValuesOnly, charset, sideChannel) {
      var obj = object;
      var tmpSc = sideChannel;
      var step = 0;
      var findFlag = false;
      while ((tmpSc = tmpSc.get(sentinel)) !== void 0 && !findFlag) {
        var pos = tmpSc.get(object);
        step += 1;
        if (typeof pos !== "undefined") {
          if (pos === step) {
            throw new RangeError("Cyclic object value");
          } else {
            findFlag = true;
          }
        }
        if (typeof tmpSc.get(sentinel) === "undefined") {
          step = 0;
        }
      }
      if (typeof filter2 === "function") {
        obj = filter2(prefix, obj);
      } else if (obj instanceof Date) {
        obj = serializeDate(obj);
      } else if (generateArrayPrefix === "comma" && isArray2(obj)) {
        obj = utils.maybeMap(obj, function(value2) {
          if (value2 instanceof Date) {
            return serializeDate(value2);
          }
          return value2;
        });
      }
      if (obj === null) {
        if (strictNullHandling) {
          return encoder && !encodeValuesOnly ? encoder(prefix, defaults2.encoder, charset, "key", format) : prefix;
        }
        obj = "";
      }
      if (isNonNullishPrimitive(obj) || utils.isBuffer(obj)) {
        if (encoder) {
          var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults2.encoder, charset, "key", format);
          return [formatter(keyValue) + "=" + formatter(encoder(obj, defaults2.encoder, charset, "value", format))];
        }
        return [formatter(prefix) + "=" + formatter(String(obj))];
      }
      var values2 = [];
      if (typeof obj === "undefined") {
        return values2;
      }
      var objKeys;
      if (generateArrayPrefix === "comma" && isArray2(obj)) {
        if (encodeValuesOnly && encoder) {
          obj = utils.maybeMap(obj, encoder);
        }
        objKeys = [{ value: obj.length > 0 ? obj.join(",") || null : void 0 }];
      } else if (isArray2(filter2)) {
        objKeys = filter2;
      } else {
        var keys2 = Object.keys(obj);
        objKeys = sort ? keys2.sort(sort) : keys2;
      }
      var encodedPrefix = encodeDotInKeys ? String(prefix).replace(/\./g, "%2E") : String(prefix);
      var adjustedPrefix = commaRoundTrip && isArray2(obj) && obj.length === 1 ? encodedPrefix + "[]" : encodedPrefix;
      if (allowEmptyArrays && isArray2(obj) && obj.length === 0) {
        return adjustedPrefix + "[]";
      }
      for (var j = 0; j < objKeys.length; ++j) {
        var key = objKeys[j];
        var value = typeof key === "object" && key && typeof key.value !== "undefined" ? key.value : obj[key];
        if (skipNulls && value === null) {
          continue;
        }
        var encodedKey = allowDots && encodeDotInKeys ? String(key).replace(/\./g, "%2E") : String(key);
        var keyPrefix = isArray2(obj) ? typeof generateArrayPrefix === "function" ? generateArrayPrefix(adjustedPrefix, encodedKey) : adjustedPrefix : adjustedPrefix + (allowDots ? "." + encodedKey : "[" + encodedKey + "]");
        sideChannel.set(object, step);
        var valueSideChannel = getSideChannel();
        valueSideChannel.set(sentinel, sideChannel);
        pushToArray(values2, stringify3(
          value,
          keyPrefix,
          generateArrayPrefix,
          commaRoundTrip,
          allowEmptyArrays,
          strictNullHandling,
          skipNulls,
          encodeDotInKeys,
          generateArrayPrefix === "comma" && encodeValuesOnly && isArray2(obj) ? null : encoder,
          filter2,
          sort,
          allowDots,
          serializeDate,
          format,
          formatter,
          encodeValuesOnly,
          charset,
          valueSideChannel
        ));
      }
      return values2;
    };
    var normalizeStringifyOptions = function normalizeStringifyOptions2(opts) {
      if (!opts) {
        return defaults2;
      }
      if (typeof opts.allowEmptyArrays !== "undefined" && typeof opts.allowEmptyArrays !== "boolean") {
        throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");
      }
      if (typeof opts.encodeDotInKeys !== "undefined" && typeof opts.encodeDotInKeys !== "boolean") {
        throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");
      }
      if (opts.encoder !== null && typeof opts.encoder !== "undefined" && typeof opts.encoder !== "function") {
        throw new TypeError("Encoder has to be a function.");
      }
      var charset = opts.charset || defaults2.charset;
      if (typeof opts.charset !== "undefined" && opts.charset !== "utf-8" && opts.charset !== "iso-8859-1") {
        throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");
      }
      var format = formats["default"];
      if (typeof opts.format !== "undefined") {
        if (!has2.call(formats.formatters, opts.format)) {
          throw new TypeError("Unknown format option provided.");
        }
        format = opts.format;
      }
      var formatter = formats.formatters[format];
      var filter2 = defaults2.filter;
      if (typeof opts.filter === "function" || isArray2(opts.filter)) {
        filter2 = opts.filter;
      }
      var arrayFormat;
      if (opts.arrayFormat in arrayPrefixGenerators) {
        arrayFormat = opts.arrayFormat;
      } else if ("indices" in opts) {
        arrayFormat = opts.indices ? "indices" : "repeat";
      } else {
        arrayFormat = defaults2.arrayFormat;
      }
      if ("commaRoundTrip" in opts && typeof opts.commaRoundTrip !== "boolean") {
        throw new TypeError("`commaRoundTrip` must be a boolean, or absent");
      }
      var allowDots = typeof opts.allowDots === "undefined" ? opts.encodeDotInKeys === true ? true : defaults2.allowDots : !!opts.allowDots;
      return {
        addQueryPrefix: typeof opts.addQueryPrefix === "boolean" ? opts.addQueryPrefix : defaults2.addQueryPrefix,
        allowDots,
        allowEmptyArrays: typeof opts.allowEmptyArrays === "boolean" ? !!opts.allowEmptyArrays : defaults2.allowEmptyArrays,
        arrayFormat,
        charset,
        charsetSentinel: typeof opts.charsetSentinel === "boolean" ? opts.charsetSentinel : defaults2.charsetSentinel,
        commaRoundTrip: !!opts.commaRoundTrip,
        delimiter: typeof opts.delimiter === "undefined" ? defaults2.delimiter : opts.delimiter,
        encode: typeof opts.encode === "boolean" ? opts.encode : defaults2.encode,
        encodeDotInKeys: typeof opts.encodeDotInKeys === "boolean" ? opts.encodeDotInKeys : defaults2.encodeDotInKeys,
        encoder: typeof opts.encoder === "function" ? opts.encoder : defaults2.encoder,
        encodeValuesOnly: typeof opts.encodeValuesOnly === "boolean" ? opts.encodeValuesOnly : defaults2.encodeValuesOnly,
        filter: filter2,
        format,
        formatter,
        serializeDate: typeof opts.serializeDate === "function" ? opts.serializeDate : defaults2.serializeDate,
        skipNulls: typeof opts.skipNulls === "boolean" ? opts.skipNulls : defaults2.skipNulls,
        sort: typeof opts.sort === "function" ? opts.sort : null,
        strictNullHandling: typeof opts.strictNullHandling === "boolean" ? opts.strictNullHandling : defaults2.strictNullHandling
      };
    };
    module.exports = function(object, opts) {
      var obj = object;
      var options = normalizeStringifyOptions(opts);
      var objKeys;
      var filter2;
      if (typeof options.filter === "function") {
        filter2 = options.filter;
        obj = filter2("", obj);
      } else if (isArray2(options.filter)) {
        filter2 = options.filter;
        objKeys = filter2;
      }
      var keys2 = [];
      if (typeof obj !== "object" || obj === null) {
        return "";
      }
      var generateArrayPrefix = arrayPrefixGenerators[options.arrayFormat];
      var commaRoundTrip = generateArrayPrefix === "comma" && options.commaRoundTrip;
      if (!objKeys) {
        objKeys = Object.keys(obj);
      }
      if (options.sort) {
        objKeys.sort(options.sort);
      }
      var sideChannel = getSideChannel();
      for (var i = 0; i < objKeys.length; ++i) {
        var key = objKeys[i];
        var value = obj[key];
        if (options.skipNulls && value === null) {
          continue;
        }
        pushToArray(keys2, stringify2(
          value,
          key,
          generateArrayPrefix,
          commaRoundTrip,
          options.allowEmptyArrays,
          options.strictNullHandling,
          options.skipNulls,
          options.encodeDotInKeys,
          options.encode ? options.encoder : null,
          options.filter,
          options.sort,
          options.allowDots,
          options.serializeDate,
          options.format,
          options.formatter,
          options.encodeValuesOnly,
          options.charset,
          sideChannel
        ));
      }
      var joined = keys2.join(options.delimiter);
      var prefix = options.addQueryPrefix === true ? "?" : "";
      if (options.charsetSentinel) {
        if (options.charset === "iso-8859-1") {
          prefix += "utf8=%26%2310003%3B&";
        } else {
          prefix += "utf8=%E2%9C%93&";
        }
      }
      return joined.length > 0 ? prefix + joined : "";
    };
  }
});

// node_modules/qs/lib/parse.js
var require_parse = __commonJS({
  "node_modules/qs/lib/parse.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    var has2 = Object.prototype.hasOwnProperty;
    var isArray2 = Array.isArray;
    var defaults2 = {
      allowDots: false,
      allowEmptyArrays: false,
      allowPrototypes: false,
      allowSparse: false,
      arrayLimit: 20,
      charset: "utf-8",
      charsetSentinel: false,
      comma: false,
      decodeDotInKeys: false,
      decoder: utils.decode,
      delimiter: "&",
      depth: 5,
      duplicates: "combine",
      ignoreQueryPrefix: false,
      interpretNumericEntities: false,
      parameterLimit: 1e3,
      parseArrays: true,
      plainObjects: false,
      strictDepth: false,
      strictNullHandling: false,
      throwOnLimitExceeded: false
    };
    var interpretNumericEntities = function(str) {
      return str.replace(/&#(\d+);/g, function($0, numberStr) {
        return String.fromCharCode(parseInt(numberStr, 10));
      });
    };
    var parseArrayValue = function(val, options, currentArrayLength) {
      if (val && typeof val === "string" && options.comma && val.indexOf(",") > -1) {
        return val.split(",");
      }
      if (options.throwOnLimitExceeded && currentArrayLength >= options.arrayLimit) {
        throw new RangeError("Array limit exceeded. Only " + options.arrayLimit + " element" + (options.arrayLimit === 1 ? "" : "s") + " allowed in an array.");
      }
      return val;
    };
    var isoSentinel = "utf8=%26%2310003%3B";
    var charsetSentinel = "utf8=%E2%9C%93";
    var parseValues = function parseQueryStringValues(str, options) {
      var obj = { __proto__: null };
      var cleanStr = options.ignoreQueryPrefix ? str.replace(/^\?/, "") : str;
      cleanStr = cleanStr.replace(/%5B/gi, "[").replace(/%5D/gi, "]");
      var limit = options.parameterLimit === Infinity ? void 0 : options.parameterLimit;
      var parts = cleanStr.split(
        options.delimiter,
        options.throwOnLimitExceeded ? limit + 1 : limit
      );
      if (options.throwOnLimitExceeded && parts.length > limit) {
        throw new RangeError("Parameter limit exceeded. Only " + limit + " parameter" + (limit === 1 ? "" : "s") + " allowed.");
      }
      var skipIndex = -1;
      var i;
      var charset = options.charset;
      if (options.charsetSentinel) {
        for (i = 0; i < parts.length; ++i) {
          if (parts[i].indexOf("utf8=") === 0) {
            if (parts[i] === charsetSentinel) {
              charset = "utf-8";
            } else if (parts[i] === isoSentinel) {
              charset = "iso-8859-1";
            }
            skipIndex = i;
            i = parts.length;
          }
        }
      }
      for (i = 0; i < parts.length; ++i) {
        if (i === skipIndex) {
          continue;
        }
        var part = parts[i];
        var bracketEqualsPos = part.indexOf("]=");
        var pos = bracketEqualsPos === -1 ? part.indexOf("=") : bracketEqualsPos + 1;
        var key;
        var val;
        if (pos === -1) {
          key = options.decoder(part, defaults2.decoder, charset, "key");
          val = options.strictNullHandling ? null : "";
        } else {
          key = options.decoder(part.slice(0, pos), defaults2.decoder, charset, "key");
          val = utils.maybeMap(
            parseArrayValue(
              part.slice(pos + 1),
              options,
              isArray2(obj[key]) ? obj[key].length : 0
            ),
            function(encodedVal) {
              return options.decoder(encodedVal, defaults2.decoder, charset, "value");
            }
          );
        }
        if (val && options.interpretNumericEntities && charset === "iso-8859-1") {
          val = interpretNumericEntities(String(val));
        }
        if (part.indexOf("[]=") > -1) {
          val = isArray2(val) ? [val] : val;
        }
        var existing = has2.call(obj, key);
        if (existing && options.duplicates === "combine") {
          obj[key] = utils.combine(obj[key], val);
        } else if (!existing || options.duplicates === "last") {
          obj[key] = val;
        }
      }
      return obj;
    };
    var parseObject = function(chain, val, options, valuesParsed) {
      var currentArrayLength = 0;
      if (chain.length > 0 && chain[chain.length - 1] === "[]") {
        var parentKey = chain.slice(0, -1).join("");
        currentArrayLength = Array.isArray(val) && val[parentKey] ? val[parentKey].length : 0;
      }
      var leaf = valuesParsed ? val : parseArrayValue(val, options, currentArrayLength);
      for (var i = chain.length - 1; i >= 0; --i) {
        var obj;
        var root = chain[i];
        if (root === "[]" && options.parseArrays) {
          obj = options.allowEmptyArrays && (leaf === "" || options.strictNullHandling && leaf === null) ? [] : utils.combine([], leaf);
        } else {
          obj = options.plainObjects ? { __proto__: null } : {};
          var cleanRoot = root.charAt(0) === "[" && root.charAt(root.length - 1) === "]" ? root.slice(1, -1) : root;
          var decodedRoot = options.decodeDotInKeys ? cleanRoot.replace(/%2E/g, ".") : cleanRoot;
          var index = parseInt(decodedRoot, 10);
          if (!options.parseArrays && decodedRoot === "") {
            obj = { 0: leaf };
          } else if (!isNaN(index) && root !== decodedRoot && String(index) === decodedRoot && index >= 0 && (options.parseArrays && index <= options.arrayLimit)) {
            obj = [];
            obj[index] = leaf;
          } else if (decodedRoot !== "__proto__") {
            obj[decodedRoot] = leaf;
          }
        }
        leaf = obj;
      }
      return leaf;
    };
    var parseKeys = function parseQueryStringKeys(givenKey, val, options, valuesParsed) {
      if (!givenKey) {
        return;
      }
      var key = options.allowDots ? givenKey.replace(/\.([^.[]+)/g, "[$1]") : givenKey;
      var brackets = /(\[[^[\]]*])/;
      var child = /(\[[^[\]]*])/g;
      var segment = options.depth > 0 && brackets.exec(key);
      var parent = segment ? key.slice(0, segment.index) : key;
      var keys2 = [];
      if (parent) {
        if (!options.plainObjects && has2.call(Object.prototype, parent)) {
          if (!options.allowPrototypes) {
            return;
          }
        }
        keys2.push(parent);
      }
      var i = 0;
      while (options.depth > 0 && (segment = child.exec(key)) !== null && i < options.depth) {
        i += 1;
        if (!options.plainObjects && has2.call(Object.prototype, segment[1].slice(1, -1))) {
          if (!options.allowPrototypes) {
            return;
          }
        }
        keys2.push(segment[1]);
      }
      if (segment) {
        if (options.strictDepth === true) {
          throw new RangeError("Input depth exceeded depth option of " + options.depth + " and strictDepth is true");
        }
        keys2.push("[" + key.slice(segment.index) + "]");
      }
      return parseObject(keys2, val, options, valuesParsed);
    };
    var normalizeParseOptions = function normalizeParseOptions2(opts) {
      if (!opts) {
        return defaults2;
      }
      if (typeof opts.allowEmptyArrays !== "undefined" && typeof opts.allowEmptyArrays !== "boolean") {
        throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");
      }
      if (typeof opts.decodeDotInKeys !== "undefined" && typeof opts.decodeDotInKeys !== "boolean") {
        throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");
      }
      if (opts.decoder !== null && typeof opts.decoder !== "undefined" && typeof opts.decoder !== "function") {
        throw new TypeError("Decoder has to be a function.");
      }
      if (typeof opts.charset !== "undefined" && opts.charset !== "utf-8" && opts.charset !== "iso-8859-1") {
        throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");
      }
      if (typeof opts.throwOnLimitExceeded !== "undefined" && typeof opts.throwOnLimitExceeded !== "boolean") {
        throw new TypeError("`throwOnLimitExceeded` option must be a boolean");
      }
      var charset = typeof opts.charset === "undefined" ? defaults2.charset : opts.charset;
      var duplicates = typeof opts.duplicates === "undefined" ? defaults2.duplicates : opts.duplicates;
      if (duplicates !== "combine" && duplicates !== "first" && duplicates !== "last") {
        throw new TypeError("The duplicates option must be either combine, first, or last");
      }
      var allowDots = typeof opts.allowDots === "undefined" ? opts.decodeDotInKeys === true ? true : defaults2.allowDots : !!opts.allowDots;
      return {
        allowDots,
        allowEmptyArrays: typeof opts.allowEmptyArrays === "boolean" ? !!opts.allowEmptyArrays : defaults2.allowEmptyArrays,
        allowPrototypes: typeof opts.allowPrototypes === "boolean" ? opts.allowPrototypes : defaults2.allowPrototypes,
        allowSparse: typeof opts.allowSparse === "boolean" ? opts.allowSparse : defaults2.allowSparse,
        arrayLimit: typeof opts.arrayLimit === "number" ? opts.arrayLimit : defaults2.arrayLimit,
        charset,
        charsetSentinel: typeof opts.charsetSentinel === "boolean" ? opts.charsetSentinel : defaults2.charsetSentinel,
        comma: typeof opts.comma === "boolean" ? opts.comma : defaults2.comma,
        decodeDotInKeys: typeof opts.decodeDotInKeys === "boolean" ? opts.decodeDotInKeys : defaults2.decodeDotInKeys,
        decoder: typeof opts.decoder === "function" ? opts.decoder : defaults2.decoder,
        delimiter: typeof opts.delimiter === "string" || utils.isRegExp(opts.delimiter) ? opts.delimiter : defaults2.delimiter,
        // eslint-disable-next-line no-implicit-coercion, no-extra-parens
        depth: typeof opts.depth === "number" || opts.depth === false ? +opts.depth : defaults2.depth,
        duplicates,
        ignoreQueryPrefix: opts.ignoreQueryPrefix === true,
        interpretNumericEntities: typeof opts.interpretNumericEntities === "boolean" ? opts.interpretNumericEntities : defaults2.interpretNumericEntities,
        parameterLimit: typeof opts.parameterLimit === "number" ? opts.parameterLimit : defaults2.parameterLimit,
        parseArrays: opts.parseArrays !== false,
        plainObjects: typeof opts.plainObjects === "boolean" ? opts.plainObjects : defaults2.plainObjects,
        strictDepth: typeof opts.strictDepth === "boolean" ? !!opts.strictDepth : defaults2.strictDepth,
        strictNullHandling: typeof opts.strictNullHandling === "boolean" ? opts.strictNullHandling : defaults2.strictNullHandling,
        throwOnLimitExceeded: typeof opts.throwOnLimitExceeded === "boolean" ? opts.throwOnLimitExceeded : false
      };
    };
    module.exports = function(str, opts) {
      var options = normalizeParseOptions(opts);
      if (str === "" || str === null || typeof str === "undefined") {
        return options.plainObjects ? { __proto__: null } : {};
      }
      var tempObj = typeof str === "string" ? parseValues(str, options) : str;
      var obj = options.plainObjects ? { __proto__: null } : {};
      var keys2 = Object.keys(tempObj);
      for (var i = 0; i < keys2.length; ++i) {
        var key = keys2[i];
        var newObj = parseKeys(key, tempObj[key], options, typeof str === "string");
        obj = utils.merge(obj, newObj, options);
      }
      if (options.allowSparse === true) {
        return obj;
      }
      return utils.compact(obj);
    };
  }
});

// node_modules/qs/lib/index.js
var require_lib = __commonJS({
  "node_modules/qs/lib/index.js"(exports, module) {
    "use strict";
    var stringify2 = require_stringify();
    var parse2 = require_parse();
    var formats = require_formats();
    module.exports = {
      formats,
      parse: parse2,
      stringify: stringify2
    };
  }
});

// node_modules/@inertiajs/core/dist/index.esm.js
var qs = __toESM(require_lib());

// node_modules/es-toolkit/dist/array/chunk.mjs
function chunk(arr, size2) {
  if (!Number.isInteger(size2) || size2 <= 0) {
    throw new Error("Size must be an integer greater than zero.");
  }
  const chunkLength = Math.ceil(arr.length / size2);
  const result2 = Array(chunkLength);
  for (let index = 0; index < chunkLength; index++) {
    const start3 = index * size2;
    const end = start3 + size2;
    result2[index] = arr.slice(start3, end);
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/compact.mjs
function compact(arr) {
  const result2 = [];
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    if (item) {
      result2.push(item);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/difference.mjs
function difference(firstArr, secondArr) {
  const secondSet = new Set(secondArr);
  return firstArr.filter((item) => !secondSet.has(item));
}

// node_modules/es-toolkit/dist/array/differenceBy.mjs
function differenceBy(firstArr, secondArr, mapper) {
  const mappedSecondSet = new Set(secondArr.map((item) => mapper(item)));
  return firstArr.filter((item) => {
    return !mappedSecondSet.has(mapper(item));
  });
}

// node_modules/es-toolkit/dist/array/differenceWith.mjs
function differenceWith(firstArr, secondArr, areItemsEqual) {
  return firstArr.filter((firstItem) => {
    return secondArr.every((secondItem) => {
      return !areItemsEqual(firstItem, secondItem);
    });
  });
}

// node_modules/es-toolkit/dist/array/drop.mjs
function drop(arr, itemsCount) {
  itemsCount = Math.max(itemsCount, 0);
  return arr.slice(itemsCount);
}

// node_modules/es-toolkit/dist/array/dropRight.mjs
function dropRight(arr, itemsCount) {
  itemsCount = Math.min(-itemsCount, 0);
  if (itemsCount === 0) {
    return arr.slice();
  }
  return arr.slice(0, itemsCount);
}

// node_modules/es-toolkit/dist/array/dropRightWhile.mjs
function dropRightWhile(arr, canContinueDropping) {
  for (let i = arr.length - 1; i >= 0; i--) {
    if (!canContinueDropping(arr[i], i, arr)) {
      return arr.slice(0, i + 1);
    }
  }
  return [];
}

// node_modules/es-toolkit/dist/array/dropWhile.mjs
function dropWhile(arr, canContinueDropping) {
  const dropEndIndex = arr.findIndex((item, index, arr2) => !canContinueDropping(item, index, arr2));
  if (dropEndIndex === -1) {
    return [];
  }
  return arr.slice(dropEndIndex);
}

// node_modules/es-toolkit/dist/array/fill.mjs
function fill(array, value, start3 = 0, end = array.length) {
  const length = array.length;
  const finalStart = Math.max(start3 >= 0 ? start3 : length + start3, 0);
  const finalEnd = Math.min(end >= 0 ? end : length + end, length);
  for (let i = finalStart; i < finalEnd; i++) {
    array[i] = value;
  }
  return array;
}

// node_modules/es-toolkit/dist/array/flatten.mjs
function flatten(arr, depth = 1) {
  const result2 = [];
  const flooredDepth = Math.floor(depth);
  const recursive = (arr2, currentDepth) => {
    for (let i = 0; i < arr2.length; i++) {
      const item = arr2[i];
      if (Array.isArray(item) && currentDepth < flooredDepth) {
        recursive(item, currentDepth + 1);
      } else {
        result2.push(item);
      }
    }
  };
  recursive(arr, 0);
  return result2;
}

// node_modules/es-toolkit/dist/array/groupBy.mjs
function groupBy(arr, getKeyFromItem) {
  const result2 = {};
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    const key = getKeyFromItem(item);
    if (!Object.hasOwn(result2, key)) {
      result2[key] = [];
    }
    result2[key].push(item);
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/head.mjs
function head(arr) {
  return arr[0];
}

// node_modules/es-toolkit/dist/array/initial.mjs
function initial(arr) {
  return arr.slice(0, -1);
}

// node_modules/es-toolkit/dist/array/intersection.mjs
function intersection(firstArr, secondArr) {
  const secondSet = new Set(secondArr);
  return firstArr.filter((item) => {
    return secondSet.has(item);
  });
}

// node_modules/es-toolkit/dist/array/intersectionBy.mjs
function intersectionBy(firstArr, secondArr, mapper) {
  const mappedSecondSet = new Set(secondArr.map(mapper));
  return firstArr.filter((item) => mappedSecondSet.has(mapper(item)));
}

// node_modules/es-toolkit/dist/array/intersectionWith.mjs
function intersectionWith(firstArr, secondArr, areItemsEqual) {
  return firstArr.filter((firstItem) => {
    return secondArr.some((secondItem) => {
      return areItemsEqual(firstItem, secondItem);
    });
  });
}

// node_modules/es-toolkit/dist/array/last.mjs
function last(arr) {
  return arr[arr.length - 1];
}

// node_modules/es-toolkit/dist/array/maxBy.mjs
function maxBy(items, getValue) {
  if (items.length === 0) {
    return void 0;
  }
  let maxElement = items[0];
  let max2 = getValue(maxElement);
  for (let i = 1; i < items.length; i++) {
    const element = items[i];
    const value = getValue(element);
    if (value > max2) {
      max2 = value;
      maxElement = element;
    }
  }
  return maxElement;
}

// node_modules/es-toolkit/dist/array/minBy.mjs
function minBy(items, getValue) {
  if (items.length === 0) {
    return void 0;
  }
  let minElement = items[0];
  let min2 = getValue(minElement);
  for (let i = 1; i < items.length; i++) {
    const element = items[i];
    const value = getValue(element);
    if (value < min2) {
      min2 = value;
      minElement = element;
    }
  }
  return minElement;
}

// node_modules/es-toolkit/dist/array/pull.mjs
function pull(arr, valuesToRemove) {
  const valuesSet = new Set(valuesToRemove);
  let resultIndex = 0;
  for (let i = 0; i < arr.length; i++) {
    if (valuesSet.has(arr[i])) {
      continue;
    }
    if (!Object.hasOwn(arr, i)) {
      delete arr[resultIndex++];
      continue;
    }
    arr[resultIndex++] = arr[i];
  }
  arr.length = resultIndex;
  return arr;
}

// node_modules/es-toolkit/dist/array/remove.mjs
function remove(arr, shouldRemoveElement) {
  const originalArr = arr.slice();
  const removed = [];
  let resultIndex = 0;
  for (let i = 0; i < arr.length; i++) {
    if (shouldRemoveElement(arr[i], i, originalArr)) {
      removed.push(arr[i]);
      continue;
    }
    if (!Object.hasOwn(arr, i)) {
      delete arr[resultIndex++];
      continue;
    }
    arr[resultIndex++] = arr[i];
  }
  arr.length = resultIndex;
  return removed;
}

// node_modules/es-toolkit/dist/array/sample.mjs
function sample(arr) {
  const randomIndex = Math.floor(Math.random() * arr.length);
  return arr[randomIndex];
}

// node_modules/es-toolkit/dist/math/random.mjs
function random(minimum, maximum) {
  if (maximum == null) {
    maximum = minimum;
    minimum = 0;
  }
  if (minimum >= maximum) {
    throw new Error("Invalid input: The maximum value must be greater than the minimum value.");
  }
  return Math.random() * (maximum - minimum) + minimum;
}

// node_modules/es-toolkit/dist/math/randomInt.mjs
function randomInt(minimum, maximum) {
  return Math.floor(random(minimum, maximum));
}

// node_modules/es-toolkit/dist/array/sampleSize.mjs
function sampleSize(array, size2) {
  if (size2 > array.length) {
    throw new Error("Size must be less than or equal to the length of array.");
  }
  const result2 = new Array(size2);
  const selected = /* @__PURE__ */ new Set();
  for (let step = array.length - size2, resultIndex = 0; step < array.length; step++, resultIndex++) {
    let index = randomInt(0, step + 1);
    if (selected.has(index)) {
      index = step;
    }
    selected.add(index);
    result2[resultIndex] = array[index];
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/shuffle.mjs
function shuffle(arr) {
  const result2 = arr.slice();
  for (let i = result2.length - 1; i >= 1; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [result2[i], result2[j]] = [result2[j], result2[i]];
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/tail.mjs
function tail(arr) {
  return arr.slice(1);
}

// node_modules/es-toolkit/dist/compat/predicate/isSymbol.mjs
function isSymbol(value) {
  return typeof value === "symbol" || value instanceof Symbol;
}

// node_modules/es-toolkit/dist/compat/util/toNumber.mjs
function toNumber(value) {
  if (isSymbol(value)) {
    return NaN;
  }
  return Number(value);
}

// node_modules/es-toolkit/dist/compat/util/toFinite.mjs
function toFinite(value) {
  if (!value) {
    return value === 0 ? value : 0;
  }
  value = toNumber(value);
  if (value === Infinity || value === -Infinity) {
    const sign = value < 0 ? -1 : 1;
    return sign * Number.MAX_VALUE;
  }
  return value === value ? value : 0;
}

// node_modules/es-toolkit/dist/compat/util/toInteger.mjs
function toInteger(value) {
  const finite = toFinite(value);
  const remainder = finite % 1;
  return remainder ? finite - remainder : finite;
}

// node_modules/es-toolkit/dist/array/take.mjs
function take(arr, count, guard) {
  count = guard || count === void 0 ? 1 : toInteger(count);
  return arr.slice(0, count);
}

// node_modules/es-toolkit/dist/array/takeRight.mjs
function takeRight(arr, count = 1, guard) {
  count = guard || count === void 0 ? 1 : toInteger(count);
  if (count <= 0 || arr == null || arr.length === 0) {
    return [];
  }
  return arr.slice(-count);
}

// node_modules/es-toolkit/dist/array/uniq.mjs
function uniq(arr) {
  return Array.from(new Set(arr));
}

// node_modules/es-toolkit/dist/array/uniqBy.mjs
function uniqBy(arr, mapper) {
  const map2 = /* @__PURE__ */ new Map();
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    const key = mapper(item);
    if (!map2.has(key)) {
      map2.set(key, item);
    }
  }
  return Array.from(map2.values());
}

// node_modules/es-toolkit/dist/array/uniqWith.mjs
function uniqWith(arr, areItemsEqual) {
  const result2 = [];
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    const isUniq = result2.every((v) => !areItemsEqual(v, item));
    if (isUniq) {
      result2.push(item);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/unzip.mjs
function unzip(zipped) {
  let maxLen = 0;
  for (let i = 0; i < zipped.length; i++) {
    if (zipped[i].length > maxLen) {
      maxLen = zipped[i].length;
    }
  }
  const result2 = new Array(maxLen);
  for (let i = 0; i < maxLen; i++) {
    result2[i] = new Array(zipped.length);
    for (let j = 0; j < zipped.length; j++) {
      result2[i][j] = zipped[j][i];
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/windowed.mjs
function windowed(arr, size2, step = 1, { partialWindows = false } = {}) {
  if (size2 <= 0 || !Number.isInteger(size2)) {
    throw new Error("Size must be a positive integer.");
  }
  if (step <= 0 || !Number.isInteger(step)) {
    throw new Error("Step must be a positive integer.");
  }
  const result2 = [];
  const end = partialWindows ? arr.length : arr.length - size2 + 1;
  for (let i = 0; i < end; i += step) {
    result2.push(arr.slice(i, i + size2));
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/without.mjs
function without(array, ...values2) {
  return difference(array, values2);
}

// node_modules/es-toolkit/dist/array/zip.mjs
function zip(...arrs) {
  let rowCount = 0;
  for (let i = 0; i < arrs.length; i++) {
    if (arrs[i].length > rowCount) {
      rowCount = arrs[i].length;
    }
  }
  const columnCount = arrs.length;
  const result2 = Array(rowCount);
  for (let i = 0; i < rowCount; ++i) {
    const row = Array(columnCount);
    for (let j = 0; j < columnCount; ++j) {
      row[j] = arrs[j][i];
    }
    result2[i] = row;
  }
  return result2;
}

// node_modules/es-toolkit/dist/function/after.mjs
function after(n, func) {
  if (!Number.isInteger(n) || n < 0) {
    throw new Error(`n must be a non-negative integer.`);
  }
  let counter = 0;
  return (...args) => {
    if (++counter >= n) {
      return func(...args);
    }
    return void 0;
  };
}

// node_modules/es-toolkit/dist/function/ary.mjs
function ary(func, n) {
  return function(...args) {
    return func.apply(this, args.slice(0, n));
  };
}

// node_modules/es-toolkit/dist/function/debounce.mjs
function debounce(func, debounceMs, { signal, edges } = {}) {
  let pendingThis = void 0;
  let pendingArgs = null;
  const leading = edges != null && edges.includes("leading");
  const trailing = edges == null || edges.includes("trailing");
  const invoke2 = () => {
    if (pendingArgs !== null) {
      func.apply(pendingThis, pendingArgs);
      pendingThis = void 0;
      pendingArgs = null;
    }
  };
  const onTimerEnd = () => {
    if (trailing) {
      invoke2();
    }
    cancel();
  };
  let timeoutId = null;
  const schedule = () => {
    if (timeoutId != null) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      timeoutId = null;
      onTimerEnd();
    }, debounceMs);
  };
  const cancelTimer = () => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };
  const cancel = () => {
    cancelTimer();
    pendingThis = void 0;
    pendingArgs = null;
  };
  const flush = () => {
    invoke2();
  };
  const debounced = function(...args) {
    if (signal?.aborted) {
      return;
    }
    pendingThis = this;
    pendingArgs = args;
    const isFirstCall = timeoutId == null;
    schedule();
    if (leading && isFirstCall) {
      invoke2();
    }
  };
  debounced.schedule = schedule;
  debounced.cancel = cancel;
  debounced.flush = flush;
  signal?.addEventListener("abort", cancel, { once: true });
  return debounced;
}

// node_modules/es-toolkit/dist/function/flow.mjs
function flow(...funcs) {
  return function(...args) {
    let result2 = funcs.length ? funcs[0].apply(this, args) : args[0];
    for (let i = 1; i < funcs.length; i++) {
      result2 = funcs[i].call(this, result2);
    }
    return result2;
  };
}

// node_modules/es-toolkit/dist/function/flowRight.mjs
function flowRight(...funcs) {
  return flow(...funcs.reverse());
}

// node_modules/es-toolkit/dist/function/identity.mjs
function identity(x) {
  return x;
}

// node_modules/es-toolkit/dist/function/negate.mjs
function negate(func) {
  return (...args) => !func(...args);
}

// node_modules/es-toolkit/dist/function/noop.mjs
function noop() {
}

// node_modules/es-toolkit/dist/function/once.mjs
function once(func) {
  let called = false;
  let cache;
  return function(...args) {
    if (!called) {
      called = true;
      cache = func(...args);
    }
    return cache;
  };
}

// node_modules/es-toolkit/dist/function/partial.mjs
function partial(func, ...partialArgs) {
  return partialImpl(func, placeholderSymbol, ...partialArgs);
}
function partialImpl(func, placeholder, ...partialArgs) {
  const partialed = function(...providedArgs) {
    let providedArgsIndex = 0;
    const substitutedArgs = partialArgs.slice().map((arg) => arg === placeholder ? providedArgs[providedArgsIndex++] : arg);
    const remainingArgs = providedArgs.slice(providedArgsIndex);
    return func.apply(this, substitutedArgs.concat(remainingArgs));
  };
  if (func.prototype) {
    partialed.prototype = Object.create(func.prototype);
  }
  return partialed;
}
var placeholderSymbol = Symbol("partial.placeholder");
partial.placeholder = placeholderSymbol;

// node_modules/es-toolkit/dist/function/partialRight.mjs
function partialRight(func, ...partialArgs) {
  return partialRightImpl(func, placeholderSymbol2, ...partialArgs);
}
function partialRightImpl(func, placeholder, ...partialArgs) {
  const partialedRight = function(...providedArgs) {
    const placeholderLength = partialArgs.filter((arg) => arg === placeholder).length;
    const rangeLength = Math.max(providedArgs.length - placeholderLength, 0);
    const remainingArgs = providedArgs.slice(0, rangeLength);
    let providedArgsIndex = rangeLength;
    const substitutedArgs = partialArgs.slice().map((arg) => arg === placeholder ? providedArgs[providedArgsIndex++] : arg);
    return func.apply(this, remainingArgs.concat(substitutedArgs));
  };
  if (func.prototype) {
    partialedRight.prototype = Object.create(func.prototype);
  }
  return partialedRight;
}
var placeholderSymbol2 = Symbol("partialRight.placeholder");
partialRight.placeholder = placeholderSymbol2;

// node_modules/es-toolkit/dist/function/rest.mjs
function rest(func, startIndex = func.length - 1) {
  return function(...args) {
    const rest3 = args.slice(startIndex);
    const params = args.slice(0, startIndex);
    while (params.length < startIndex) {
      params.push(void 0);
    }
    return func.apply(this, [...params, rest3]);
  };
}

// node_modules/es-toolkit/dist/function/retry.mjs
var DEFAULT_RETRIES = Number.POSITIVE_INFINITY;

// node_modules/es-toolkit/dist/math/clamp.mjs
function clamp(value, bound1, bound2) {
  if (bound2 == null) {
    return Math.min(value, bound1);
  }
  return Math.min(Math.max(value, bound1), bound2);
}

// node_modules/es-toolkit/dist/math/inRange.mjs
function inRange(value, minimum, maximum) {
  if (maximum == null) {
    maximum = minimum;
    minimum = 0;
  }
  if (minimum >= maximum) {
    throw new Error("The maximum value must be greater than the minimum value.");
  }
  return minimum <= value && value < maximum;
}

// node_modules/es-toolkit/dist/math/sum.mjs
function sum(nums) {
  let result2 = 0;
  for (let i = 0; i < nums.length; i++) {
    result2 += nums[i];
  }
  return result2;
}

// node_modules/es-toolkit/dist/math/mean.mjs
function mean(nums) {
  return sum(nums) / nums.length;
}

// node_modules/es-toolkit/dist/math/meanBy.mjs
function meanBy(items, getValue) {
  const nums = items.map((x) => getValue(x));
  return mean(nums);
}

// node_modules/es-toolkit/dist/math/range.mjs
function range(start3, end, step = 1) {
  if (end == null) {
    end = start3;
    start3 = 0;
  }
  if (!Number.isInteger(step) || step === 0) {
    throw new Error(`The step value must be a non-zero integer.`);
  }
  const length = Math.max(Math.ceil((end - start3) / step), 0);
  const result2 = new Array(length);
  for (let i = 0; i < length; i++) {
    result2[i] = start3 + i * step;
  }
  return result2;
}

// node_modules/es-toolkit/dist/predicate/isPrimitive.mjs
function isPrimitive(value) {
  return value == null || typeof value !== "object" && typeof value !== "function";
}

// node_modules/es-toolkit/dist/predicate/isTypedArray.mjs
function isTypedArray(x) {
  return ArrayBuffer.isView(x) && !(x instanceof DataView);
}

// node_modules/es-toolkit/dist/object/clone.mjs
function clone(obj) {
  if (isPrimitive(obj)) {
    return obj;
  }
  if (Array.isArray(obj) || isTypedArray(obj) || obj instanceof ArrayBuffer || typeof SharedArrayBuffer !== "undefined" && obj instanceof SharedArrayBuffer) {
    return obj.slice(0);
  }
  const prototype = Object.getPrototypeOf(obj);
  const Constructor = prototype.constructor;
  if (obj instanceof Date || obj instanceof Map || obj instanceof Set) {
    return new Constructor(obj);
  }
  if (obj instanceof RegExp) {
    const newRegExp = new Constructor(obj);
    newRegExp.lastIndex = obj.lastIndex;
    return newRegExp;
  }
  if (obj instanceof DataView) {
    return new Constructor(obj.buffer.slice(0));
  }
  if (obj instanceof Error) {
    const newError = new Constructor(obj.message);
    newError.stack = obj.stack;
    newError.name = obj.name;
    newError.cause = obj.cause;
    return newError;
  }
  if (typeof File !== "undefined" && obj instanceof File) {
    const newFile = new Constructor([obj], obj.name, { type: obj.type, lastModified: obj.lastModified });
    return newFile;
  }
  if (typeof obj === "object") {
    const newObject = Object.create(prototype);
    return Object.assign(newObject, obj);
  }
  return obj;
}

// node_modules/es-toolkit/dist/compat/_internal/getSymbols.mjs
function getSymbols(object) {
  return Object.getOwnPropertySymbols(object).filter((symbol) => Object.prototype.propertyIsEnumerable.call(object, symbol));
}

// node_modules/es-toolkit/dist/compat/_internal/getTag.mjs
function getTag(value) {
  if (value == null) {
    return value === void 0 ? "[object Undefined]" : "[object Null]";
  }
  return Object.prototype.toString.call(value);
}

// node_modules/es-toolkit/dist/compat/_internal/tags.mjs
var regexpTag = "[object RegExp]";
var stringTag = "[object String]";
var numberTag = "[object Number]";
var booleanTag = "[object Boolean]";
var argumentsTag = "[object Arguments]";
var symbolTag = "[object Symbol]";
var dateTag = "[object Date]";
var mapTag = "[object Map]";
var setTag = "[object Set]";
var arrayTag = "[object Array]";
var functionTag = "[object Function]";
var arrayBufferTag = "[object ArrayBuffer]";
var objectTag = "[object Object]";
var errorTag = "[object Error]";
var dataViewTag = "[object DataView]";
var uint8ArrayTag = "[object Uint8Array]";
var uint8ClampedArrayTag = "[object Uint8ClampedArray]";
var uint16ArrayTag = "[object Uint16Array]";
var uint32ArrayTag = "[object Uint32Array]";
var bigUint64ArrayTag = "[object BigUint64Array]";
var int8ArrayTag = "[object Int8Array]";
var int16ArrayTag = "[object Int16Array]";
var int32ArrayTag = "[object Int32Array]";
var bigInt64ArrayTag = "[object BigInt64Array]";
var float32ArrayTag = "[object Float32Array]";
var float64ArrayTag = "[object Float64Array]";

// node_modules/es-toolkit/dist/object/cloneDeepWith.mjs
function cloneDeepWith(obj, cloneValue) {
  return cloneDeepWithImpl(obj, void 0, obj, /* @__PURE__ */ new Map(), cloneValue);
}
function cloneDeepWithImpl(valueToClone, keyToClone, objectToClone, stack = /* @__PURE__ */ new Map(), cloneValue = void 0) {
  const cloned = cloneValue?.(valueToClone, keyToClone, objectToClone, stack);
  if (cloned != null) {
    return cloned;
  }
  if (isPrimitive(valueToClone)) {
    return valueToClone;
  }
  if (stack.has(valueToClone)) {
    return stack.get(valueToClone);
  }
  if (Array.isArray(valueToClone)) {
    const result2 = new Array(valueToClone.length);
    stack.set(valueToClone, result2);
    for (let i = 0; i < valueToClone.length; i++) {
      result2[i] = cloneDeepWithImpl(valueToClone[i], i, objectToClone, stack, cloneValue);
    }
    if (Object.hasOwn(valueToClone, "index")) {
      result2.index = valueToClone.index;
    }
    if (Object.hasOwn(valueToClone, "input")) {
      result2.input = valueToClone.input;
    }
    return result2;
  }
  if (valueToClone instanceof Date) {
    return new Date(valueToClone.getTime());
  }
  if (valueToClone instanceof RegExp) {
    const result2 = new RegExp(valueToClone.source, valueToClone.flags);
    result2.lastIndex = valueToClone.lastIndex;
    return result2;
  }
  if (valueToClone instanceof Map) {
    const result2 = /* @__PURE__ */ new Map();
    stack.set(valueToClone, result2);
    for (const [key, value] of valueToClone) {
      result2.set(key, cloneDeepWithImpl(value, key, objectToClone, stack, cloneValue));
    }
    return result2;
  }
  if (valueToClone instanceof Set) {
    const result2 = /* @__PURE__ */ new Set();
    stack.set(valueToClone, result2);
    for (const value of valueToClone) {
      result2.add(cloneDeepWithImpl(value, void 0, objectToClone, stack, cloneValue));
    }
    return result2;
  }
  if (typeof Buffer !== "undefined" && Buffer.isBuffer(valueToClone)) {
    return valueToClone.subarray();
  }
  if (isTypedArray(valueToClone)) {
    const result2 = new (Object.getPrototypeOf(valueToClone)).constructor(valueToClone.length);
    stack.set(valueToClone, result2);
    for (let i = 0; i < valueToClone.length; i++) {
      result2[i] = cloneDeepWithImpl(valueToClone[i], i, objectToClone, stack, cloneValue);
    }
    return result2;
  }
  if (valueToClone instanceof ArrayBuffer || typeof SharedArrayBuffer !== "undefined" && valueToClone instanceof SharedArrayBuffer) {
    return valueToClone.slice(0);
  }
  if (valueToClone instanceof DataView) {
    const result2 = new DataView(valueToClone.buffer.slice(0), valueToClone.byteOffset, valueToClone.byteLength);
    stack.set(valueToClone, result2);
    copyProperties(result2, valueToClone, objectToClone, stack, cloneValue);
    return result2;
  }
  if (typeof File !== "undefined" && valueToClone instanceof File) {
    const result2 = new File([valueToClone], valueToClone.name, {
      type: valueToClone.type
    });
    stack.set(valueToClone, result2);
    copyProperties(result2, valueToClone, objectToClone, stack, cloneValue);
    return result2;
  }
  if (valueToClone instanceof Blob) {
    const result2 = new Blob([valueToClone], { type: valueToClone.type });
    stack.set(valueToClone, result2);
    copyProperties(result2, valueToClone, objectToClone, stack, cloneValue);
    return result2;
  }
  if (valueToClone instanceof Error) {
    const result2 = new valueToClone.constructor();
    stack.set(valueToClone, result2);
    result2.message = valueToClone.message;
    result2.name = valueToClone.name;
    result2.stack = valueToClone.stack;
    result2.cause = valueToClone.cause;
    copyProperties(result2, valueToClone, objectToClone, stack, cloneValue);
    return result2;
  }
  if (typeof valueToClone === "object" && isCloneableObject(valueToClone)) {
    const result2 = Object.create(Object.getPrototypeOf(valueToClone));
    stack.set(valueToClone, result2);
    copyProperties(result2, valueToClone, objectToClone, stack, cloneValue);
    return result2;
  }
  return valueToClone;
}
function copyProperties(target, source, objectToClone = target, stack, cloneValue) {
  const keys2 = [...Object.keys(source), ...getSymbols(source)];
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const descriptor = Object.getOwnPropertyDescriptor(target, key);
    if (descriptor == null || descriptor.writable) {
      target[key] = cloneDeepWithImpl(source[key], key, objectToClone, stack, cloneValue);
    }
  }
}
function isCloneableObject(object) {
  switch (getTag(object)) {
    case argumentsTag:
    case arrayTag:
    case arrayBufferTag:
    case dataViewTag:
    case booleanTag:
    case dateTag:
    case float32ArrayTag:
    case float64ArrayTag:
    case int8ArrayTag:
    case int16ArrayTag:
    case int32ArrayTag:
    case mapTag:
    case numberTag:
    case objectTag:
    case regexpTag:
    case setTag:
    case stringTag:
    case symbolTag:
    case uint8ArrayTag:
    case uint8ClampedArrayTag:
    case uint16ArrayTag:
    case uint32ArrayTag: {
      return true;
    }
    default: {
      return false;
    }
  }
}

// node_modules/es-toolkit/dist/object/cloneDeep.mjs
function cloneDeep(obj) {
  return cloneDeepWithImpl(obj, void 0, obj, /* @__PURE__ */ new Map(), void 0);
}

// node_modules/es-toolkit/dist/object/findKey.mjs
function findKey(obj, predicate) {
  const keys2 = Object.keys(obj);
  return keys2.find((key) => predicate(obj[key], key, obj));
}

// node_modules/es-toolkit/dist/predicate/isPlainObject.mjs
function isPlainObject(value) {
  if (!value || typeof value !== "object") {
    return false;
  }
  const proto = Object.getPrototypeOf(value);
  const hasObjectPrototype = proto === null || proto === Object.prototype || Object.getPrototypeOf(proto) === null;
  if (!hasObjectPrototype) {
    return false;
  }
  return Object.prototype.toString.call(value) === "[object Object]";
}

// node_modules/es-toolkit/dist/object/invert.mjs
function invert(obj) {
  const result2 = {};
  const keys2 = Object.keys(obj);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = obj[key];
    result2[value] = key;
  }
  return result2;
}

// node_modules/es-toolkit/dist/object/mapKeys.mjs
function mapKeys(object, getNewKey) {
  const result2 = {};
  const keys2 = Object.keys(object);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = object[key];
    result2[getNewKey(value, key, object)] = value;
  }
  return result2;
}

// node_modules/es-toolkit/dist/object/mapValues.mjs
function mapValues(object, getNewValue) {
  const result2 = {};
  const keys2 = Object.keys(object);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = object[key];
    result2[key] = getNewValue(value, key, object);
  }
  return result2;
}

// node_modules/es-toolkit/dist/_internal/isUnsafeProperty.mjs
function isUnsafeProperty(key) {
  return key === "__proto__";
}

// node_modules/es-toolkit/dist/compat/predicate/isObjectLike.mjs
function isObjectLike(value) {
  return typeof value === "object" && value !== null;
}

// node_modules/es-toolkit/dist/compat/predicate/isArray.mjs
function isArray(value) {
  return Array.isArray(value);
}

// node_modules/es-toolkit/dist/string/capitalize.mjs
function capitalize(str) {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

// node_modules/es-toolkit/dist/string/words.mjs
var CASE_SPLIT_PATTERN = new RegExp("\\p{Lu}?\\p{Ll}+|[0-9]+|\\p{Lu}+(?!\\p{Ll})|\\p{Emoji_Presentation}|\\p{Extended_Pictographic}|\\p{L}+", "gu");
function words(str) {
  return Array.from(str.match(CASE_SPLIT_PATTERN) ?? []);
}

// node_modules/es-toolkit/dist/string/camelCase.mjs
function camelCase(str) {
  const words$1 = words(str);
  if (words$1.length === 0) {
    return "";
  }
  const [first, ...rest3] = words$1;
  return `${first.toLowerCase()}${rest3.map((word) => capitalize(word)).join("")}`;
}

// node_modules/es-toolkit/dist/compat/predicate/isPlainObject.mjs
function isPlainObject2(object) {
  if (typeof object !== "object") {
    return false;
  }
  if (object == null) {
    return false;
  }
  if (Object.getPrototypeOf(object) === null) {
    return true;
  }
  if (Object.prototype.toString.call(object) !== "[object Object]") {
    const tag = object[Symbol.toStringTag];
    if (tag == null) {
      return false;
    }
    const isTagReadonly = !Object.getOwnPropertyDescriptor(object, Symbol.toStringTag)?.writable;
    if (isTagReadonly) {
      return false;
    }
    return object.toString() === `[object ${tag}]`;
  }
  let proto = object;
  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto);
  }
  return Object.getPrototypeOf(object) === proto;
}

// node_modules/es-toolkit/dist/string/snakeCase.mjs
function snakeCase(str) {
  const words$1 = words(str);
  return words$1.map((word) => word.toLowerCase()).join("_");
}

// node_modules/es-toolkit/dist/predicate/isArrayBuffer.mjs
function isArrayBuffer(value) {
  return value instanceof ArrayBuffer;
}

// node_modules/es-toolkit/dist/predicate/isBuffer.mjs
function isBuffer(x) {
  return typeof Buffer !== "undefined" && Buffer.isBuffer(x);
}

// node_modules/es-toolkit/dist/predicate/isDate.mjs
function isDate(value) {
  return value instanceof Date;
}

// node_modules/es-toolkit/dist/compat/util/eq.mjs
function eq(value, other) {
  return value === other || Number.isNaN(value) && Number.isNaN(other);
}

// node_modules/es-toolkit/dist/predicate/isEqualWith.mjs
function isEqualWith(a, b, areValuesEqual) {
  return isEqualWithImpl(a, b, void 0, void 0, void 0, void 0, areValuesEqual);
}
function isEqualWithImpl(a, b, property2, aParent, bParent, stack, areValuesEqual) {
  const result2 = areValuesEqual(a, b, property2, aParent, bParent, stack);
  if (result2 !== void 0) {
    return result2;
  }
  if (typeof a === typeof b) {
    switch (typeof a) {
      case "bigint":
      case "string":
      case "boolean":
      case "symbol":
      case "undefined": {
        return a === b;
      }
      case "number": {
        return a === b || Object.is(a, b);
      }
      case "function": {
        return a === b;
      }
      case "object": {
        return areObjectsEqual(a, b, stack, areValuesEqual);
      }
    }
  }
  return areObjectsEqual(a, b, stack, areValuesEqual);
}
function areObjectsEqual(a, b, stack, areValuesEqual) {
  if (Object.is(a, b)) {
    return true;
  }
  let aTag = getTag(a);
  let bTag = getTag(b);
  if (aTag === argumentsTag) {
    aTag = objectTag;
  }
  if (bTag === argumentsTag) {
    bTag = objectTag;
  }
  if (aTag !== bTag) {
    return false;
  }
  switch (aTag) {
    case stringTag:
      return a.toString() === b.toString();
    case numberTag: {
      const x = a.valueOf();
      const y = b.valueOf();
      return eq(x, y);
    }
    case booleanTag:
    case dateTag:
    case symbolTag:
      return Object.is(a.valueOf(), b.valueOf());
    case regexpTag: {
      return a.source === b.source && a.flags === b.flags;
    }
    case functionTag: {
      return a === b;
    }
  }
  stack = stack ?? /* @__PURE__ */ new Map();
  const aStack = stack.get(a);
  const bStack = stack.get(b);
  if (aStack != null && bStack != null) {
    return aStack === b;
  }
  stack.set(a, b);
  stack.set(b, a);
  try {
    switch (aTag) {
      case mapTag: {
        if (a.size !== b.size) {
          return false;
        }
        for (const [key, value] of a.entries()) {
          if (!b.has(key) || !isEqualWithImpl(value, b.get(key), key, a, b, stack, areValuesEqual)) {
            return false;
          }
        }
        return true;
      }
      case setTag: {
        if (a.size !== b.size) {
          return false;
        }
        const aValues = Array.from(a.values());
        const bValues = Array.from(b.values());
        for (let i = 0; i < aValues.length; i++) {
          const aValue = aValues[i];
          const index = bValues.findIndex((bValue) => {
            return isEqualWithImpl(aValue, bValue, void 0, a, b, stack, areValuesEqual);
          });
          if (index === -1) {
            return false;
          }
          bValues.splice(index, 1);
        }
        return true;
      }
      case arrayTag:
      case uint8ArrayTag:
      case uint8ClampedArrayTag:
      case uint16ArrayTag:
      case uint32ArrayTag:
      case bigUint64ArrayTag:
      case int8ArrayTag:
      case int16ArrayTag:
      case int32ArrayTag:
      case bigInt64ArrayTag:
      case float32ArrayTag:
      case float64ArrayTag: {
        if (typeof Buffer !== "undefined" && Buffer.isBuffer(a) !== Buffer.isBuffer(b)) {
          return false;
        }
        if (a.length !== b.length) {
          return false;
        }
        for (let i = 0; i < a.length; i++) {
          if (!isEqualWithImpl(a[i], b[i], i, a, b, stack, areValuesEqual)) {
            return false;
          }
        }
        return true;
      }
      case arrayBufferTag: {
        if (a.byteLength !== b.byteLength) {
          return false;
        }
        return areObjectsEqual(new Uint8Array(a), new Uint8Array(b), stack, areValuesEqual);
      }
      case dataViewTag: {
        if (a.byteLength !== b.byteLength || a.byteOffset !== b.byteOffset) {
          return false;
        }
        return areObjectsEqual(new Uint8Array(a), new Uint8Array(b), stack, areValuesEqual);
      }
      case errorTag: {
        return a.name === b.name && a.message === b.message;
      }
      case objectTag: {
        const areEqualInstances = areObjectsEqual(a.constructor, b.constructor, stack, areValuesEqual) || isPlainObject(a) && isPlainObject(b);
        if (!areEqualInstances) {
          return false;
        }
        const aKeys = [...Object.keys(a), ...getSymbols(a)];
        const bKeys = [...Object.keys(b), ...getSymbols(b)];
        if (aKeys.length !== bKeys.length) {
          return false;
        }
        for (let i = 0; i < aKeys.length; i++) {
          const propKey = aKeys[i];
          const aProp = a[propKey];
          if (!Object.hasOwn(b, propKey)) {
            return false;
          }
          const bProp = b[propKey];
          if (!isEqualWithImpl(aProp, bProp, propKey, a, b, stack, areValuesEqual)) {
            return false;
          }
        }
        return true;
      }
      default: {
        return false;
      }
    }
  } finally {
    stack.delete(a);
    stack.delete(b);
  }
}

// node_modules/es-toolkit/dist/predicate/isEqual.mjs
function isEqual(a, b) {
  return isEqualWith(a, b, noop);
}

// node_modules/es-toolkit/dist/predicate/isFunction.mjs
function isFunction(value) {
  return typeof value === "function";
}

// node_modules/es-toolkit/dist/predicate/isLength.mjs
function isLength(value) {
  return Number.isSafeInteger(value) && value >= 0;
}

// node_modules/es-toolkit/dist/predicate/isMap.mjs
function isMap(value) {
  return value instanceof Map;
}

// node_modules/es-toolkit/dist/predicate/isNil.mjs
function isNil(x) {
  return x == null;
}

// node_modules/es-toolkit/dist/predicate/isNull.mjs
function isNull(x) {
  return x === null;
}

// node_modules/es-toolkit/dist/predicate/isRegExp.mjs
function isRegExp(value) {
  return value instanceof RegExp;
}

// node_modules/es-toolkit/dist/predicate/isSet.mjs
function isSet(value) {
  return value instanceof Set;
}

// node_modules/es-toolkit/dist/predicate/isSymbol.mjs
function isSymbol2(value) {
  return typeof value === "symbol";
}

// node_modules/es-toolkit/dist/predicate/isUndefined.mjs
function isUndefined(x) {
  return x === void 0;
}

// node_modules/es-toolkit/dist/predicate/isWeakMap.mjs
function isWeakMap(value) {
  return value instanceof WeakMap;
}

// node_modules/es-toolkit/dist/predicate/isWeakSet.mjs
function isWeakSet(value) {
  return value instanceof WeakSet;
}

// node_modules/es-toolkit/dist/string/deburr.mjs
var deburrMap = new Map(Object.entries({
  Æ: "Ae",
  Ð: "D",
  Ø: "O",
  Þ: "Th",
  ß: "ss",
  æ: "ae",
  ð: "d",
  ø: "o",
  þ: "th",
  Đ: "D",
  đ: "d",
  Ħ: "H",
  ħ: "h",
  ı: "i",
  Ĳ: "IJ",
  ĳ: "ij",
  ĸ: "k",
  Ŀ: "L",
  ŀ: "l",
  Ł: "L",
  ł: "l",
  ŉ: "'n",
  Ŋ: "N",
  ŋ: "n",
  Œ: "Oe",
  œ: "oe",
  Ŧ: "T",
  ŧ: "t",
  ſ: "s"
}));
function deburr(str) {
  str = str.normalize("NFD");
  let result2 = "";
  for (let i = 0; i < str.length; i++) {
    const char = str[i];
    if (char >= "̀" && char <= "ͯ" || char >= "︠" && char <= "︣") {
      continue;
    }
    result2 += deburrMap.get(char) ?? char;
  }
  return result2;
}

// node_modules/es-toolkit/dist/string/escape.mjs
var htmlEscapes = {
  "&": "&amp;",
  "<": "&lt;",
  ">": "&gt;",
  '"': "&quot;",
  "'": "&#39;"
};
function escape2(str) {
  return str.replace(/[&<>"']/g, (match) => htmlEscapes[match]);
}

// node_modules/es-toolkit/dist/string/escapeRegExp.mjs
function escapeRegExp(str) {
  return str.replace(/[\\^$.*+?()[\]{}|]/g, "\\$&");
}

// node_modules/es-toolkit/dist/string/kebabCase.mjs
function kebabCase(str) {
  const words$1 = words(str);
  return words$1.map((word) => word.toLowerCase()).join("-");
}

// node_modules/es-toolkit/dist/string/lowerCase.mjs
function lowerCase(str) {
  const words$1 = words(str);
  return words$1.map((word) => word.toLowerCase()).join(" ");
}

// node_modules/es-toolkit/dist/string/lowerFirst.mjs
function lowerFirst(str) {
  return str.substring(0, 1).toLowerCase() + str.substring(1);
}

// node_modules/es-toolkit/dist/string/pad.mjs
function pad(str, length, chars = " ") {
  return str.padStart(Math.floor((length - str.length) / 2) + str.length, chars).padEnd(length, chars);
}

// node_modules/es-toolkit/dist/string/trimEnd.mjs
function trimEnd(str, chars) {
  if (chars === void 0) {
    return str.trimEnd();
  }
  let endIndex = str.length;
  switch (typeof chars) {
    case "string": {
      if (chars.length !== 1) {
        throw new Error(`The 'chars' parameter should be a single character string.`);
      }
      while (endIndex > 0 && str[endIndex - 1] === chars) {
        endIndex--;
      }
      break;
    }
    case "object": {
      while (endIndex > 0 && chars.includes(str[endIndex - 1])) {
        endIndex--;
      }
    }
  }
  return str.substring(0, endIndex);
}

// node_modules/es-toolkit/dist/string/trimStart.mjs
function trimStart(str, chars) {
  if (chars === void 0) {
    return str.trimStart();
  }
  let startIndex = 0;
  switch (typeof chars) {
    case "string": {
      while (startIndex < str.length && str[startIndex] === chars) {
        startIndex++;
      }
      break;
    }
    case "object": {
      while (startIndex < str.length && chars.includes(str[startIndex])) {
        startIndex++;
      }
    }
  }
  return str.substring(startIndex);
}

// node_modules/es-toolkit/dist/string/trim.mjs
function trim(str, chars) {
  if (chars === void 0) {
    return str.trim();
  }
  return trimStart(trimEnd(str, chars), chars);
}

// node_modules/es-toolkit/dist/string/unescape.mjs
var htmlUnescapes = {
  "&amp;": "&",
  "&lt;": "<",
  "&gt;": ">",
  "&quot;": '"',
  "&#39;": "'"
};
function unescape2(str) {
  return str.replace(/&(?:amp|lt|gt|quot|#(0+)?39);/g, (match) => htmlUnescapes[match] || "'");
}

// node_modules/es-toolkit/dist/string/upperCase.mjs
function upperCase(str) {
  const words$1 = words(str);
  let result2 = "";
  for (let i = 0; i < words$1.length; i++) {
    result2 += words$1[i].toUpperCase();
    if (i < words$1.length - 1) {
      result2 += " ";
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/string/upperFirst.mjs
function upperFirst(str) {
  return str.substring(0, 1).toUpperCase() + str.substring(1);
}

// node_modules/@inertiajs/core/dist/index.esm.js
function debounce2(fn, delay3) {
  let timeoutID;
  return function(...args) {
    clearTimeout(timeoutID);
    timeoutID = setTimeout(() => fn.apply(this, args), delay3);
  };
}
function fireEvent(name, options) {
  return document.dispatchEvent(new CustomEvent(`inertia:${name}`, options));
}
var fireBeforeEvent = (visit) => {
  return fireEvent("before", { cancelable: true, detail: { visit } });
};
var fireErrorEvent = (errors) => {
  return fireEvent("error", { detail: { errors } });
};
var fireExceptionEvent = (exception) => {
  return fireEvent("exception", { cancelable: true, detail: { exception } });
};
var fireFinishEvent = (visit) => {
  return fireEvent("finish", { detail: { visit } });
};
var fireInvalidEvent = (response) => {
  return fireEvent("invalid", { cancelable: true, detail: { response } });
};
var fireNavigateEvent = (page2) => {
  return fireEvent("navigate", { detail: { page: page2 } });
};
var fireProgressEvent = (progress3) => {
  return fireEvent("progress", { detail: { progress: progress3 } });
};
var fireStartEvent = (visit) => {
  return fireEvent("start", { detail: { visit } });
};
var fireSuccessEvent = (page2) => {
  return fireEvent("success", { detail: { page: page2 } });
};
var firePrefetchedEvent = (response, visit) => {
  return fireEvent("prefetched", { detail: { fetchedAt: Date.now(), response: response.data, visit } });
};
var firePrefetchingEvent = (visit) => {
  return fireEvent("prefetching", { detail: { visit } });
};
var SessionStorage = class {
  static set(key, value) {
    if (typeof window !== "undefined") {
      window.sessionStorage.setItem(key, JSON.stringify(value));
    }
  }
  static get(key) {
    if (typeof window !== "undefined") {
      return JSON.parse(window.sessionStorage.getItem(key) || "null");
    }
  }
  static merge(key, value) {
    const existing = this.get(key);
    if (existing === null) {
      this.set(key, value);
    } else {
      this.set(key, { ...existing, ...value });
    }
  }
  static remove(key) {
    if (typeof window !== "undefined") {
      window.sessionStorage.removeItem(key);
    }
  }
  static removeNested(key, nestedKey) {
    const existing = this.get(key);
    if (existing !== null) {
      delete existing[nestedKey];
      this.set(key, existing);
    }
  }
  static exists(key) {
    try {
      return this.get(key) !== null;
    } catch (error) {
      return false;
    }
  }
  static clear() {
    if (typeof window !== "undefined") {
      window.sessionStorage.clear();
    }
  }
};
SessionStorage.locationVisitKey = "inertiaLocationVisit";
var encryptHistory = async (data) => {
  if (typeof window === "undefined") {
    throw new Error("Unable to encrypt history");
  }
  const iv = getIv();
  const storedKey = await getKeyFromSessionStorage();
  const key = await getOrCreateKey(storedKey);
  if (!key) {
    throw new Error("Unable to encrypt history");
  }
  const encrypted = await encryptData(iv, key, data);
  return encrypted;
};
var historySessionStorageKeys = {
  key: "historyKey",
  iv: "historyIv"
};
var decryptHistory = async (data) => {
  const iv = getIv();
  const storedKey = await getKeyFromSessionStorage();
  if (!storedKey) {
    throw new Error("Unable to decrypt history");
  }
  return await decryptData(iv, storedKey, data);
};
var encryptData = async (iv, key, data) => {
  if (typeof window === "undefined") {
    throw new Error("Unable to encrypt history");
  }
  if (typeof window.crypto.subtle === "undefined") {
    console.warn("Encryption is not supported in this environment. SSL is required.");
    return Promise.resolve(data);
  }
  const textEncoder = new TextEncoder();
  const str = JSON.stringify(data);
  const encoded = new Uint8Array(str.length * 3);
  const result2 = textEncoder.encodeInto(str, encoded);
  return window.crypto.subtle.encrypt(
    {
      name: "AES-GCM",
      iv
    },
    key,
    encoded.subarray(0, result2.written)
  );
};
var decryptData = async (iv, key, data) => {
  if (typeof window.crypto.subtle === "undefined") {
    console.warn("Decryption is not supported in this environment. SSL is required.");
    return Promise.resolve(data);
  }
  const decrypted = await window.crypto.subtle.decrypt(
    {
      name: "AES-GCM",
      iv
    },
    key,
    data
  );
  return JSON.parse(new TextDecoder().decode(decrypted));
};
var getIv = () => {
  const ivString = SessionStorage.get(historySessionStorageKeys.iv);
  if (ivString) {
    return new Uint8Array(ivString);
  }
  const iv = window.crypto.getRandomValues(new Uint8Array(12));
  SessionStorage.set(historySessionStorageKeys.iv, Array.from(iv));
  return iv;
};
var createKey = async () => {
  if (typeof window.crypto.subtle === "undefined") {
    console.warn("Encryption is not supported in this environment. SSL is required.");
    return Promise.resolve(null);
  }
  return window.crypto.subtle.generateKey(
    {
      name: "AES-GCM",
      length: 256
    },
    true,
    ["encrypt", "decrypt"]
  );
};
var saveKey = async (key) => {
  if (typeof window.crypto.subtle === "undefined") {
    console.warn("Encryption is not supported in this environment. SSL is required.");
    return Promise.resolve();
  }
  const keyData = await window.crypto.subtle.exportKey("raw", key);
  SessionStorage.set(historySessionStorageKeys.key, Array.from(new Uint8Array(keyData)));
};
var getOrCreateKey = async (key) => {
  if (key) {
    return key;
  }
  const newKey = await createKey();
  if (!newKey) {
    return null;
  }
  await saveKey(newKey);
  return newKey;
};
var getKeyFromSessionStorage = async () => {
  const stringKey = SessionStorage.get(historySessionStorageKeys.key);
  if (!stringKey) {
    return null;
  }
  const key = await window.crypto.subtle.importKey(
    "raw",
    new Uint8Array(stringKey),
    {
      name: "AES-GCM",
      length: 256
    },
    true,
    ["encrypt", "decrypt"]
  );
  return key;
};
var Scroll = class {
  static save() {
    history.saveScrollPositions(
      Array.from(this.regions()).map((region) => ({
        top: region.scrollTop,
        left: region.scrollLeft
      }))
    );
  }
  static regions() {
    return document.querySelectorAll("[scroll-region]");
  }
  static reset() {
    const anchorHash = typeof window !== "undefined" ? window.location.hash : null;
    if (!anchorHash) {
      window.scrollTo(0, 0);
    }
    this.regions().forEach((region) => {
      if (typeof region.scrollTo === "function") {
        region.scrollTo(0, 0);
      } else {
        region.scrollTop = 0;
        region.scrollLeft = 0;
      }
    });
    this.save();
    if (anchorHash) {
      setTimeout(() => {
        const anchorElement = document.getElementById(anchorHash.slice(1));
        anchorElement ? anchorElement.scrollIntoView() : window.scrollTo(0, 0);
      });
    }
  }
  static restore(scrollRegions) {
    this.restoreDocument();
    this.regions().forEach((region, index) => {
      const scrollPosition = scrollRegions[index];
      if (!scrollPosition) {
        return;
      }
      if (typeof region.scrollTo === "function") {
        region.scrollTo(scrollPosition.left, scrollPosition.top);
      } else {
        region.scrollTop = scrollPosition.top;
        region.scrollLeft = scrollPosition.left;
      }
    });
  }
  static restoreDocument() {
    const scrollPosition = history.getDocumentScrollPosition();
    if (typeof window !== "undefined") {
      window.scrollTo(scrollPosition.left, scrollPosition.top);
    }
  }
  static onScroll(event) {
    const target = event.target;
    if (typeof target.hasAttribute === "function" && target.hasAttribute("scroll-region")) {
      this.save();
    }
  }
  static onWindowScroll() {
    history.saveDocumentScrollPosition({
      top: window.scrollY,
      left: window.scrollX
    });
  }
};
function hasFiles(data) {
  return data instanceof File || data instanceof Blob || data instanceof FileList && data.length > 0 || data instanceof FormData && Array.from(data.values()).some((value) => hasFiles(value)) || typeof data === "object" && data !== null && Object.values(data).some((value) => hasFiles(value));
}
var isFormData = (value) => value instanceof FormData;
function objectToFormData(source, form = new FormData(), parentKey = null) {
  source = source || {};
  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key)) {
      append(form, composeKey(parentKey, key), source[key]);
    }
  }
  return form;
}
function composeKey(parent, key) {
  return parent ? parent + "[" + key + "]" : key;
}
function append(form, key, value) {
  if (Array.isArray(value)) {
    return Array.from(value.keys()).forEach((index) => append(form, composeKey(key, index.toString()), value[index]));
  } else if (value instanceof Date) {
    return form.append(key, value.toISOString());
  } else if (value instanceof File) {
    return form.append(key, value, value.name);
  } else if (value instanceof Blob) {
    return form.append(key, value);
  } else if (typeof value === "boolean") {
    return form.append(key, value ? "1" : "0");
  } else if (typeof value === "string") {
    return form.append(key, value);
  } else if (typeof value === "number") {
    return form.append(key, `${value}`);
  } else if (value === null || value === void 0) {
    return form.append(key, "");
  }
  objectToFormData(value, form, key);
}
function hrefToUrl(href) {
  return new URL(href.toString(), typeof window === "undefined" ? void 0 : window.location.toString());
}
var transformUrlAndData = (href, data, method2, forceFormData, queryStringArrayFormat) => {
  let url = typeof href === "string" ? hrefToUrl(href) : href;
  if ((hasFiles(data) || forceFormData) && !isFormData(data)) {
    data = objectToFormData(data);
  }
  if (isFormData(data)) {
    return [url, data];
  }
  const [_href, _data] = mergeDataIntoQueryString(method2, url, data, queryStringArrayFormat);
  return [hrefToUrl(_href), _data];
};
function mergeDataIntoQueryString(method2, href, data, qsArrayFormat = "brackets") {
  const hasHost = /^[a-z][a-z0-9+.-]*:\/\//i.test(href.toString());
  const hasAbsolutePath = hasHost || href.toString().startsWith("/");
  const hasRelativePath = !hasAbsolutePath && !href.toString().startsWith("#") && !href.toString().startsWith("?");
  const hasRelativePathWithDotPrefix = /^[.]{1,2}([/]|$)/.test(href.toString());
  const hasSearch = href.toString().includes("?") || method2 === "get" && Object.keys(data).length;
  const hasHash = href.toString().includes("#");
  const url = new URL(href.toString(), typeof window === "undefined" ? "http://localhost" : window.location.toString());
  if (method2 === "get" && Object.keys(data).length) {
    const parseOptions = { ignoreQueryPrefix: true, parseArrays: false };
    url.search = qs.stringify(
      { ...qs.parse(url.search, parseOptions), ...data },
      {
        encodeValuesOnly: true,
        arrayFormat: qsArrayFormat
      }
    );
    data = {};
  }
  return [
    [
      hasHost ? `${url.protocol}//${url.host}` : "",
      hasAbsolutePath ? url.pathname : "",
      hasRelativePath ? url.pathname.substring(hasRelativePathWithDotPrefix ? 0 : 1) : "",
      hasSearch ? url.search : "",
      hasHash ? url.hash : ""
    ].join(""),
    data
  ];
}
function urlWithoutHash(url) {
  url = new URL(url.href);
  url.hash = "";
  return url;
}
var setHashIfSameUrl = (originUrl, destinationUrl) => {
  if (originUrl.hash && !destinationUrl.hash && urlWithoutHash(originUrl).href === destinationUrl.href) {
    destinationUrl.hash = originUrl.hash;
  }
};
var isSameUrlWithoutHash = (url1, url2) => {
  return urlWithoutHash(url1).href === urlWithoutHash(url2).href;
};
var CurrentPage = class {
  constructor() {
    this.componentId = {};
    this.listeners = [];
    this.isFirstPageLoad = true;
    this.cleared = false;
  }
  init({ initialPage, swapComponent: swapComponent2, resolveComponent }) {
    this.page = initialPage;
    this.swapComponent = swapComponent2;
    this.resolveComponent = resolveComponent;
    return this;
  }
  set(page2, {
    replace: replace2 = false,
    preserveScroll = false,
    preserveState = false
  } = {}) {
    this.componentId = {};
    const componentId = this.componentId;
    if (page2.clearHistory) {
      history.clear();
    }
    return this.resolve(page2.component).then((component) => {
      if (componentId !== this.componentId) {
        return;
      }
      page2.rememberedState ?? (page2.rememberedState = {});
      const location = typeof window !== "undefined" ? window.location : new URL(page2.url);
      replace2 = replace2 || isSameUrlWithoutHash(hrefToUrl(page2.url), location);
      return new Promise((resolve) => {
        replace2 ? history.replaceState(page2, () => resolve(null)) : history.pushState(page2, () => resolve(null));
      }).then(() => {
        const isNewComponent = !this.isTheSame(page2);
        this.page = page2;
        this.cleared = false;
        if (isNewComponent) {
          this.fireEventsFor("newComponent");
        }
        if (this.isFirstPageLoad) {
          this.fireEventsFor("firstLoad");
        }
        this.isFirstPageLoad = false;
        return this.swap({ component, page: page2, preserveState }).then(() => {
          if (!preserveScroll) {
            Scroll.reset();
          }
          eventHandler.fireInternalEvent("loadDeferredProps");
          if (!replace2) {
            fireNavigateEvent(page2);
          }
        });
      });
    });
  }
  setQuietly(page2, {
    preserveState = false
  } = {}) {
    return this.resolve(page2.component).then((component) => {
      this.page = page2;
      this.cleared = false;
      history.setCurrent(page2);
      return this.swap({ component, page: page2, preserveState });
    });
  }
  clear() {
    this.cleared = true;
  }
  isCleared() {
    return this.cleared;
  }
  get() {
    return this.page;
  }
  merge(data) {
    this.page = { ...this.page, ...data };
  }
  setUrlHash(hash) {
    if (!this.page.url.includes(hash)) {
      this.page.url += hash;
    }
  }
  remember(data) {
    this.page.rememberedState = data;
  }
  swap({
    component,
    page: page2,
    preserveState
  }) {
    return this.swapComponent({ component, page: page2, preserveState });
  }
  resolve(component) {
    return Promise.resolve(this.resolveComponent(component));
  }
  isTheSame(page2) {
    return this.page.component === page2.component;
  }
  on(event, callback) {
    this.listeners.push({ event, callback });
    return () => {
      this.listeners = this.listeners.filter((listener) => listener.event !== event && listener.callback !== callback);
    };
  }
  fireEventsFor(event) {
    this.listeners.filter((listener) => listener.event === event).forEach((listener) => listener.callback());
  }
};
var page = new CurrentPage();
var Queue = class {
  constructor() {
    this.items = [];
    this.processingPromise = null;
  }
  add(item) {
    this.items.push(item);
    return this.process();
  }
  process() {
    this.processingPromise ?? (this.processingPromise = this.processNext().then(() => {
      this.processingPromise = null;
    }));
    return this.processingPromise;
  }
  processNext() {
    const next = this.items.shift();
    if (next) {
      return Promise.resolve(next()).then(() => this.processNext());
    }
    return Promise.resolve();
  }
};
var isServer = typeof window === "undefined";
var queue = new Queue();
var isChromeIOS = !isServer && /CriOS/.test(window.navigator.userAgent);
var History = class {
  constructor() {
    this.rememberedState = "rememberedState";
    this.scrollRegions = "scrollRegions";
    this.preserveUrl = false;
    this.current = {};
    this.initialState = null;
  }
  remember(data, key) {
    this.replaceState({
      ...page.get(),
      rememberedState: {
        ...page.get()?.rememberedState ?? {},
        [key]: data
      }
    });
  }
  restore(key) {
    if (!isServer) {
      return this.current[this.rememberedState] ? this.current[this.rememberedState]?.[key] : this.initialState?.[this.rememberedState]?.[key];
    }
  }
  pushState(page2, cb = null) {
    if (isServer) {
      return;
    }
    if (this.preserveUrl) {
      cb && cb();
      return;
    }
    this.current = page2;
    queue.add(() => {
      return this.getPageData(page2).then((data) => {
        const doPush = () => {
          this.doPushState({ page: data }, page2.url);
          cb && cb();
        };
        if (isChromeIOS) {
          setTimeout(doPush);
        } else {
          doPush();
        }
      });
    });
  }
  getPageData(page2) {
    return new Promise((resolve) => {
      return page2.encryptHistory ? encryptHistory(page2).then(resolve) : resolve(page2);
    });
  }
  processQueue() {
    return queue.process();
  }
  decrypt(page2 = null) {
    if (isServer) {
      return Promise.resolve(page2 ?? page.get());
    }
    const pageData = page2 ?? window.history.state?.page;
    return this.decryptPageData(pageData).then((data) => {
      if (!data) {
        throw new Error("Unable to decrypt history");
      }
      if (this.initialState === null) {
        this.initialState = data ?? void 0;
      } else {
        this.current = data ?? {};
      }
      return data;
    });
  }
  decryptPageData(pageData) {
    return pageData instanceof ArrayBuffer ? decryptHistory(pageData) : Promise.resolve(pageData);
  }
  saveScrollPositions(scrollRegions) {
    queue.add(() => {
      return Promise.resolve().then(() => {
        if (!window.history.state?.page) {
          return;
        }
        this.doReplaceState(
          {
            page: window.history.state.page,
            scrollRegions
          }
        );
      });
    });
  }
  saveDocumentScrollPosition(scrollRegion) {
    queue.add(() => {
      return Promise.resolve().then(() => {
        if (!window.history.state?.page) {
          return;
        }
        this.doReplaceState(
          {
            page: window.history.state.page,
            documentScrollPosition: scrollRegion
          }
        );
      });
    });
  }
  getScrollRegions() {
    return window.history.state?.scrollRegions || [];
  }
  getDocumentScrollPosition() {
    return window.history.state?.documentScrollPosition || { top: 0, left: 0 };
  }
  replaceState(page2, cb = null) {
    page.merge(page2);
    if (isServer) {
      return;
    }
    if (this.preserveUrl) {
      cb && cb();
      return;
    }
    this.current = page2;
    queue.add(() => {
      return this.getPageData(page2).then((data) => {
        const doReplace = () => {
          this.doReplaceState({ page: data }, page2.url);
          cb && cb();
        };
        if (isChromeIOS) {
          setTimeout(doReplace);
        } else {
          doReplace();
        }
      });
    });
  }
  doReplaceState(data, url) {
    window.history.replaceState(
      {
        ...data,
        scrollRegions: data.scrollRegions ?? window.history.state?.scrollRegions,
        documentScrollPosition: data.documentScrollPosition ?? window.history.state?.documentScrollPosition
      },
      "",
      url
    );
  }
  doPushState(data, url) {
    window.history.pushState(data, "", url);
  }
  getState(key, defaultValue) {
    return this.current?.[key] ?? defaultValue;
  }
  deleteState(key) {
    if (this.current[key] !== void 0) {
      delete this.current[key];
      this.replaceState(this.current);
    }
  }
  hasAnyState() {
    return !!this.getAllState();
  }
  clear() {
    SessionStorage.remove(historySessionStorageKeys.key);
    SessionStorage.remove(historySessionStorageKeys.iv);
  }
  setCurrent(page2) {
    this.current = page2;
  }
  isValidState(state) {
    return !!state.page;
  }
  getAllState() {
    return this.current;
  }
};
if (typeof window !== "undefined" && window.history.scrollRestoration) {
  window.history.scrollRestoration = "manual";
}
var history = new History();
var EventHandler = class {
  constructor() {
    this.internalListeners = [];
  }
  init() {
    if (typeof window !== "undefined") {
      window.addEventListener("popstate", this.handlePopstateEvent.bind(this));
      window.addEventListener("scroll", debounce2(Scroll.onWindowScroll.bind(Scroll), 100), true);
    }
    if (typeof document !== "undefined") {
      document.addEventListener("scroll", debounce2(Scroll.onScroll.bind(Scroll), 100), true);
    }
  }
  onGlobalEvent(type, callback) {
    const listener = (event) => {
      const response = callback(event);
      if (event.cancelable && !event.defaultPrevented && response === false) {
        event.preventDefault();
      }
    };
    return this.registerListener(`inertia:${type}`, listener);
  }
  on(event, callback) {
    this.internalListeners.push({ event, listener: callback });
    return () => {
      this.internalListeners = this.internalListeners.filter((listener) => listener.listener !== callback);
    };
  }
  onMissingHistoryItem() {
    page.clear();
    this.fireInternalEvent("missingHistoryItem");
  }
  fireInternalEvent(event) {
    this.internalListeners.filter((listener) => listener.event === event).forEach((listener) => listener.listener());
  }
  registerListener(type, listener) {
    document.addEventListener(type, listener);
    return () => document.removeEventListener(type, listener);
  }
  handlePopstateEvent(event) {
    const state = event.state || null;
    if (state === null) {
      const url = hrefToUrl(page.get().url);
      url.hash = window.location.hash;
      history.replaceState({ ...page.get(), url: url.href });
      Scroll.reset();
      return;
    }
    if (!history.isValidState(state)) {
      return this.onMissingHistoryItem();
    }
    history.decrypt(state.page).then((data) => {
      if (page.get().version !== data.version) {
        this.onMissingHistoryItem();
        return;
      }
      router.cancelAll();
      page.setQuietly(data, { preserveState: false }).then(() => {
        window.requestAnimationFrame(() => {
          Scroll.restore(history.getScrollRegions());
        });
        fireNavigateEvent(page.get());
      });
    }).catch(() => {
      this.onMissingHistoryItem();
    });
  }
};
var eventHandler = new EventHandler();
var NavigationType = class {
  constructor() {
    this.type = this.resolveType();
  }
  resolveType() {
    if (typeof window === "undefined") {
      return "navigate";
    }
    if (window.performance && window.performance.getEntriesByType && window.performance.getEntriesByType("navigation").length > 0) {
      return window.performance.getEntriesByType("navigation")[0].type;
    }
    return "navigate";
  }
  get() {
    return this.type;
  }
  isBackForward() {
    return this.type === "back_forward";
  }
  isReload() {
    return this.type === "reload";
  }
};
var navigationType = new NavigationType();
var InitialVisit = class {
  static handle() {
    this.clearRememberedStateOnReload();
    const scenarios = [this.handleBackForward, this.handleLocation, this.handleDefault];
    scenarios.find((handler) => handler.bind(this)());
  }
  static clearRememberedStateOnReload() {
    if (navigationType.isReload()) {
      history.deleteState(history.rememberedState);
    }
  }
  static handleBackForward() {
    if (!navigationType.isBackForward() || !history.hasAnyState()) {
      return false;
    }
    const scrollRegions = history.getScrollRegions();
    history.decrypt().then((data) => {
      page.set(data, { preserveScroll: true, preserveState: true }).then(() => {
        Scroll.restore(scrollRegions);
        fireNavigateEvent(page.get());
      });
    }).catch(() => {
      eventHandler.onMissingHistoryItem();
    });
    return true;
  }
  /**
   * @link https://inertiajs.com/redirects#external-redirects
   */
  static handleLocation() {
    if (!SessionStorage.exists(SessionStorage.locationVisitKey)) {
      return false;
    }
    const locationVisit = SessionStorage.get(SessionStorage.locationVisitKey) || {};
    SessionStorage.remove(SessionStorage.locationVisitKey);
    if (typeof window !== "undefined") {
      page.setUrlHash(window.location.hash);
    }
    history.decrypt(page.get()).then(() => {
      const rememberedState = history.getState(history.rememberedState, {});
      const scrollRegions = history.getScrollRegions();
      page.remember(rememberedState);
      page.set(page.get(), {
        preserveScroll: locationVisit.preserveScroll,
        preserveState: true
      }).then(() => {
        if (locationVisit.preserveScroll) {
          Scroll.restore(scrollRegions);
        }
        fireNavigateEvent(page.get());
      });
    }).catch(() => {
      eventHandler.onMissingHistoryItem();
    });
    return true;
  }
  static handleDefault() {
    if (typeof window !== "undefined") {
      page.setUrlHash(window.location.hash);
    }
    page.set(page.get(), { preserveScroll: true, preserveState: true }).then(() => {
      if (navigationType.isReload()) {
        Scroll.restore(history.getScrollRegions());
      }
      fireNavigateEvent(page.get());
    });
  }
};
var Poll = class {
  constructor(interval, cb, options) {
    this.id = null;
    this.throttle = false;
    this.keepAlive = false;
    this.cbCount = 0;
    this.keepAlive = options.keepAlive ?? false;
    this.cb = cb;
    this.interval = interval;
    if (options.autoStart ?? true) {
      this.start();
    }
  }
  stop() {
    if (this.id) {
      clearInterval(this.id);
    }
  }
  start() {
    if (typeof window === "undefined") {
      return;
    }
    this.stop();
    this.id = window.setInterval(() => {
      if (!this.throttle || this.cbCount % 10 === 0) {
        this.cb();
      }
      if (this.throttle) {
        this.cbCount++;
      }
    }, this.interval);
  }
  isInBackground(hidden) {
    this.throttle = this.keepAlive ? false : hidden;
    if (this.throttle) {
      this.cbCount = 0;
    }
  }
};
var Polls = class {
  constructor() {
    this.polls = [];
    this.setupVisibilityListener();
  }
  add(interval, cb, options) {
    const poll = new Poll(interval, cb, options);
    this.polls.push(poll);
    return {
      stop: () => poll.stop(),
      start: () => poll.start()
    };
  }
  clear() {
    this.polls.forEach((poll) => poll.stop());
    this.polls = [];
  }
  setupVisibilityListener() {
    if (typeof document === "undefined") {
      return;
    }
    document.addEventListener(
      "visibilitychange",
      () => {
        this.polls.forEach((poll) => poll.isInBackground(document.hidden));
      },
      false
    );
  }
};
var polls = new Polls();
var objectsAreEqual = (obj1, obj2, excludeKeys) => {
  if (obj1 === obj2) {
    return true;
  }
  for (const key in obj1) {
    if (excludeKeys.includes(key)) {
      continue;
    }
    if (obj1[key] === obj2[key]) {
      continue;
    }
    if (!compareValues2(obj1[key], obj2[key])) {
      return false;
    }
  }
  return true;
};
var compareValues2 = (value1, value2) => {
  switch (typeof value1) {
    case "object":
      return objectsAreEqual(value1, value2, []);
    case "function":
      return value1.toString() === value2.toString();
    default:
      return value1 === value2;
  }
};
var conversionMap = {
  ms: 1,
  s: 1e3,
  m: 1e3 * 60,
  h: 1e3 * 60 * 60,
  d: 1e3 * 60 * 60 * 24
};
var timeToMs = (time) => {
  if (typeof time === "number") {
    return time;
  }
  for (const [unit, conversion] of Object.entries(conversionMap)) {
    if (time.endsWith(unit)) {
      return parseFloat(time) * conversion;
    }
  }
  return parseInt(time);
};
var PrefetchedRequests = class {
  constructor() {
    this.cached = [];
    this.inFlightRequests = [];
    this.removalTimers = [];
    this.currentUseId = null;
  }
  add(params, sendFunc, { cacheFor }) {
    const inFlight = this.findInFlight(params);
    if (inFlight) {
      return Promise.resolve();
    }
    const existing = this.findCached(params);
    if (!params.fresh && existing && existing.staleTimestamp > Date.now()) {
      return Promise.resolve();
    }
    const [stale, expires] = this.extractStaleValues(cacheFor);
    const promise = new Promise((resolve, reject2) => {
      sendFunc({
        ...params,
        onCancel: () => {
          this.remove(params);
          params.onCancel();
          reject2();
        },
        onError: (error) => {
          this.remove(params);
          params.onError(error);
          reject2();
        },
        onPrefetching(visitParams) {
          params.onPrefetching(visitParams);
        },
        onPrefetched(response, visit) {
          params.onPrefetched(response, visit);
        },
        onPrefetchResponse(response) {
          resolve(response);
        }
      });
    }).then((response) => {
      this.remove(params);
      this.cached.push({
        params: { ...params },
        staleTimestamp: Date.now() + stale,
        response: promise,
        singleUse: expires === 0,
        timestamp: Date.now(),
        inFlight: false
      });
      this.scheduleForRemoval(params, expires);
      this.inFlightRequests = this.inFlightRequests.filter((prefetching) => {
        return !this.paramsAreEqual(prefetching.params, params);
      });
      response.handlePrefetch();
      return response;
    });
    this.inFlightRequests.push({
      params: { ...params },
      response: promise,
      staleTimestamp: null,
      inFlight: true
    });
    return promise;
  }
  removeAll() {
    this.cached = [];
    this.removalTimers.forEach((removalTimer) => {
      clearTimeout(removalTimer.timer);
    });
    this.removalTimers = [];
  }
  remove(params) {
    this.cached = this.cached.filter((prefetched) => {
      return !this.paramsAreEqual(prefetched.params, params);
    });
    this.clearTimer(params);
  }
  extractStaleValues(cacheFor) {
    const [stale, expires] = this.cacheForToStaleAndExpires(cacheFor);
    return [timeToMs(stale), timeToMs(expires)];
  }
  cacheForToStaleAndExpires(cacheFor) {
    if (!Array.isArray(cacheFor)) {
      return [cacheFor, cacheFor];
    }
    switch (cacheFor.length) {
      case 0:
        return [0, 0];
      case 1:
        return [cacheFor[0], cacheFor[0]];
      default:
        return [cacheFor[0], cacheFor[1]];
    }
  }
  clearTimer(params) {
    const timer = this.removalTimers.find((removalTimer) => {
      return this.paramsAreEqual(removalTimer.params, params);
    });
    if (timer) {
      clearTimeout(timer.timer);
      this.removalTimers = this.removalTimers.filter((removalTimer) => removalTimer !== timer);
    }
  }
  scheduleForRemoval(params, expiresIn) {
    if (typeof window === "undefined") {
      return;
    }
    this.clearTimer(params);
    if (expiresIn > 0) {
      const timer = window.setTimeout(() => this.remove(params), expiresIn);
      this.removalTimers.push({
        params,
        timer
      });
    }
  }
  get(params) {
    return this.findCached(params) || this.findInFlight(params);
  }
  use(prefetched, params) {
    const id = `${params.url.pathname}-${Date.now()}-${Math.random().toString(36).substring(7)}`;
    this.currentUseId = id;
    return prefetched.response.then((response) => {
      if (this.currentUseId !== id) {
        return;
      }
      response.mergeParams({ ...params, onPrefetched: () => {
      } });
      this.removeSingleUseItems(params);
      return response.handle();
    });
  }
  removeSingleUseItems(params) {
    this.cached = this.cached.filter((prefetched) => {
      if (!this.paramsAreEqual(prefetched.params, params)) {
        return true;
      }
      return !prefetched.singleUse;
    });
  }
  findCached(params) {
    return this.cached.find((prefetched) => {
      return this.paramsAreEqual(prefetched.params, params);
    }) || null;
  }
  findInFlight(params) {
    return this.inFlightRequests.find((prefetched) => {
      return this.paramsAreEqual(prefetched.params, params);
    }) || null;
  }
  withoutPurposePrefetchHeader(params) {
    const newParams = cloneDeep(params);
    if (newParams.headers["Purpose"] === "prefetch") {
      delete newParams.headers["Purpose"];
    }
    return newParams;
  }
  paramsAreEqual(params1, params2) {
    return objectsAreEqual(
      this.withoutPurposePrefetchHeader(params1),
      this.withoutPurposePrefetchHeader(params2),
      [
        "showProgress",
        "replace",
        "prefetch",
        "onBefore",
        "onStart",
        "onProgress",
        "onFinish",
        "onCancel",
        "onSuccess",
        "onError",
        "onPrefetched",
        "onCancelToken",
        "onPrefetching",
        "async"
      ]
    );
  }
};
var prefetchedRequests = new PrefetchedRequests();
var RequestParams = class _RequestParams {
  constructor(params) {
    this.callbacks = [];
    if (!params.prefetch) {
      this.params = params;
    } else {
      const wrappedCallbacks = {
        onBefore: this.wrapCallback(params, "onBefore"),
        onStart: this.wrapCallback(params, "onStart"),
        onProgress: this.wrapCallback(params, "onProgress"),
        onFinish: this.wrapCallback(params, "onFinish"),
        onCancel: this.wrapCallback(params, "onCancel"),
        onSuccess: this.wrapCallback(params, "onSuccess"),
        onError: this.wrapCallback(params, "onError"),
        onCancelToken: this.wrapCallback(params, "onCancelToken"),
        onPrefetched: this.wrapCallback(params, "onPrefetched"),
        onPrefetching: this.wrapCallback(params, "onPrefetching")
      };
      this.params = {
        ...params,
        ...wrappedCallbacks,
        onPrefetchResponse: params.onPrefetchResponse || (() => {
        })
      };
    }
  }
  static create(params) {
    return new _RequestParams(params);
  }
  data() {
    return this.params.method === "get" ? null : this.params.data;
  }
  queryParams() {
    return this.params.method === "get" ? this.params.data : {};
  }
  isPartial() {
    return this.params.only.length > 0 || this.params.except.length > 0 || this.params.reset.length > 0;
  }
  onCancelToken(cb) {
    this.params.onCancelToken({
      cancel: cb
    });
  }
  markAsFinished() {
    this.params.completed = true;
    this.params.cancelled = false;
    this.params.interrupted = false;
  }
  markAsCancelled({ cancelled = true, interrupted = false }) {
    this.params.onCancel();
    this.params.completed = false;
    this.params.cancelled = cancelled;
    this.params.interrupted = interrupted;
  }
  wasCancelledAtAll() {
    return this.params.cancelled || this.params.interrupted;
  }
  onFinish() {
    this.params.onFinish(this.params);
  }
  onStart() {
    this.params.onStart(this.params);
  }
  onPrefetching() {
    this.params.onPrefetching(this.params);
  }
  onPrefetchResponse(response) {
    if (this.params.onPrefetchResponse) {
      this.params.onPrefetchResponse(response);
    }
  }
  all() {
    return this.params;
  }
  headers() {
    const headers = {
      ...this.params.headers
    };
    if (this.isPartial()) {
      headers["X-Inertia-Partial-Component"] = page.get().component;
    }
    const only = this.params.only.concat(this.params.reset);
    if (only.length > 0) {
      headers["X-Inertia-Partial-Data"] = only.join(",");
    }
    if (this.params.except.length > 0) {
      headers["X-Inertia-Partial-Except"] = this.params.except.join(",");
    }
    if (this.params.reset.length > 0) {
      headers["X-Inertia-Reset"] = this.params.reset.join(",");
    }
    if (this.params.errorBag && this.params.errorBag.length > 0) {
      headers["X-Inertia-Error-Bag"] = this.params.errorBag;
    }
    return headers;
  }
  setPreserveOptions(page2) {
    this.params.preserveScroll = this.resolvePreserveOption(this.params.preserveScroll, page2);
    this.params.preserveState = this.resolvePreserveOption(this.params.preserveState, page2);
  }
  runCallbacks() {
    this.callbacks.forEach(({ name, args }) => {
      this.params[name](...args);
    });
  }
  merge(toMerge) {
    this.params = {
      ...this.params,
      ...toMerge
    };
  }
  wrapCallback(params, name) {
    return (...args) => {
      this.recordCallback(name, args);
      params[name](...args);
    };
  }
  recordCallback(name, args) {
    this.callbacks.push({ name, args });
  }
  resolvePreserveOption(value, page2) {
    if (typeof value === "function") {
      return value(page2);
    }
    if (value === "errors") {
      return Object.keys(page2.props.errors || {}).length > 0;
    }
    return value;
  }
};
var modal_default = {
  modal: null,
  listener: null,
  show(html) {
    if (typeof html === "object") {
      html = `All Inertia requests must receive a valid Inertia response, however a plain JSON response was received.<hr>${JSON.stringify(
        html
      )}`;
    }
    const page2 = document.createElement("html");
    page2.innerHTML = html;
    page2.querySelectorAll("a").forEach((a) => a.setAttribute("target", "_top"));
    this.modal = document.createElement("div");
    this.modal.style.position = "fixed";
    this.modal.style.width = "100vw";
    this.modal.style.height = "100vh";
    this.modal.style.padding = "50px";
    this.modal.style.boxSizing = "border-box";
    this.modal.style.backgroundColor = "rgba(0, 0, 0, .6)";
    this.modal.style.zIndex = 2e5;
    this.modal.addEventListener("click", () => this.hide());
    const iframe = document.createElement("iframe");
    iframe.style.backgroundColor = "white";
    iframe.style.borderRadius = "5px";
    iframe.style.width = "100%";
    iframe.style.height = "100%";
    this.modal.appendChild(iframe);
    document.body.prepend(this.modal);
    document.body.style.overflow = "hidden";
    if (!iframe.contentWindow) {
      throw new Error("iframe not yet ready.");
    }
    iframe.contentWindow.document.open();
    iframe.contentWindow.document.write(page2.outerHTML);
    iframe.contentWindow.document.close();
    this.listener = this.hideOnEscape.bind(this);
    document.addEventListener("keydown", this.listener);
  },
  hide() {
    this.modal.outerHTML = "";
    this.modal = null;
    document.body.style.overflow = "visible";
    document.removeEventListener("keydown", this.listener);
  },
  hideOnEscape(event) {
    if (event.keyCode === 27) {
      this.hide();
    }
  }
};
var queue2 = new Queue();
var Response = class _Response {
  constructor(requestParams, response, originatingPage) {
    this.requestParams = requestParams;
    this.response = response;
    this.originatingPage = originatingPage;
  }
  static create(params, response, originatingPage) {
    return new _Response(params, response, originatingPage);
  }
  async handlePrefetch() {
    if (isSameUrlWithoutHash(this.requestParams.all().url, window.location)) {
      this.handle();
    }
  }
  async handle() {
    return queue2.add(() => this.process());
  }
  async process() {
    if (this.requestParams.all().prefetch) {
      this.requestParams.all().prefetch = false;
      this.requestParams.all().onPrefetched(this.response, this.requestParams.all());
      firePrefetchedEvent(this.response, this.requestParams.all());
      return Promise.resolve();
    }
    this.requestParams.runCallbacks();
    if (!this.isInertiaResponse()) {
      return this.handleNonInertiaResponse();
    }
    await history.processQueue();
    history.preserveUrl = this.requestParams.all().preserveUrl;
    await this.setPage();
    const errors = page.get().props.errors || {};
    if (Object.keys(errors).length > 0) {
      const scopedErrors = this.getScopedErrors(errors);
      fireErrorEvent(scopedErrors);
      return this.requestParams.all().onError(scopedErrors);
    }
    fireSuccessEvent(page.get());
    await this.requestParams.all().onSuccess(page.get());
    history.preserveUrl = false;
  }
  mergeParams(params) {
    this.requestParams.merge(params);
  }
  async handleNonInertiaResponse() {
    if (this.isLocationVisit()) {
      const locationUrl = hrefToUrl(this.getHeader("x-inertia-location"));
      setHashIfSameUrl(this.requestParams.all().url, locationUrl);
      return this.locationVisit(locationUrl);
    }
    const response = {
      ...this.response,
      data: this.getDataFromResponse(this.response.data)
    };
    if (fireInvalidEvent(response)) {
      return modal_default.show(response.data);
    }
  }
  isInertiaResponse() {
    return this.hasHeader("x-inertia");
  }
  hasStatus(status2) {
    return this.response.status === status2;
  }
  getHeader(header) {
    return this.response.headers[header];
  }
  hasHeader(header) {
    return this.getHeader(header) !== void 0;
  }
  isLocationVisit() {
    return this.hasStatus(409) && this.hasHeader("x-inertia-location");
  }
  /**
   * @link https://inertiajs.com/redirects#external-redirects
   */
  locationVisit(url) {
    try {
      SessionStorage.set(SessionStorage.locationVisitKey, {
        preserveScroll: this.requestParams.all().preserveScroll === true
      });
      if (typeof window === "undefined") {
        return;
      }
      if (isSameUrlWithoutHash(window.location, url)) {
        window.location.reload();
      } else {
        window.location.href = url.href;
      }
    } catch (error) {
      return false;
    }
  }
  async setPage() {
    const pageResponse = this.getDataFromResponse(this.response.data);
    if (!this.shouldSetPage(pageResponse)) {
      return Promise.resolve();
    }
    this.mergeProps(pageResponse);
    await this.setRememberedState(pageResponse);
    this.requestParams.setPreserveOptions(pageResponse);
    pageResponse.url = history.preserveUrl ? page.get().url : this.pageUrl(pageResponse);
    return page.set(pageResponse, {
      replace: this.requestParams.all().replace,
      preserveScroll: this.requestParams.all().preserveScroll,
      preserveState: this.requestParams.all().preserveState
    });
  }
  getDataFromResponse(response) {
    if (typeof response !== "string") {
      return response;
    }
    try {
      return JSON.parse(response);
    } catch (error) {
      return response;
    }
  }
  shouldSetPage(pageResponse) {
    if (!this.requestParams.all().async) {
      return true;
    }
    if (this.originatingPage.component !== pageResponse.component) {
      return true;
    }
    if (this.originatingPage.component !== page.get().component) {
      return false;
    }
    const originatingUrl = hrefToUrl(this.originatingPage.url);
    const currentPageUrl = hrefToUrl(page.get().url);
    return originatingUrl.origin === currentPageUrl.origin && originatingUrl.pathname === currentPageUrl.pathname;
  }
  pageUrl(pageResponse) {
    const responseUrl = hrefToUrl(pageResponse.url);
    setHashIfSameUrl(this.requestParams.all().url, responseUrl);
    return responseUrl.pathname + responseUrl.search + responseUrl.hash;
  }
  mergeProps(pageResponse) {
    if (!this.requestParams.isPartial() || pageResponse.component !== page.get().component) {
      return;
    }
    const propsToMerge = pageResponse.mergeProps || [];
    const propsToDeepMerge = pageResponse.deepMergeProps || [];
    const matchPropsOn = pageResponse.matchPropsOn || [];
    propsToMerge.forEach((prop) => {
      const incomingProp = pageResponse.props[prop];
      if (Array.isArray(incomingProp)) {
        pageResponse.props[prop] = this.mergeOrMatchItems(
          page.get().props[prop] || [],
          incomingProp,
          prop,
          matchPropsOn
        );
      } else if (typeof incomingProp === "object" && incomingProp !== null) {
        pageResponse.props[prop] = {
          ...page.get().props[prop] || [],
          ...incomingProp
        };
      }
    });
    propsToDeepMerge.forEach((prop) => {
      const incomingProp = pageResponse.props[prop];
      const currentProp = page.get().props[prop];
      const deepMerge = (target, source, currentKey) => {
        if (Array.isArray(source)) {
          return this.mergeOrMatchItems(target, source, currentKey, matchPropsOn);
        }
        if (typeof source === "object" && source !== null) {
          return Object.keys(source).reduce(
            (acc, key) => {
              acc[key] = deepMerge(target ? target[key] : void 0, source[key], `${currentKey}.${key}`);
              return acc;
            },
            { ...target }
          );
        }
        return source;
      };
      pageResponse.props[prop] = deepMerge(currentProp, incomingProp, prop);
    });
    pageResponse.props = { ...page.get().props, ...pageResponse.props };
  }
  mergeOrMatchItems(target, source, currentKey, matchPropsOn) {
    const matchOn = matchPropsOn.find((key) => {
      const path = key.split(".").slice(0, -1).join(".");
      return path === currentKey;
    });
    if (!matchOn) {
      return [...Array.isArray(target) ? target : [], ...source];
    }
    const uniqueProperty = matchOn.split(".").pop() || "";
    const targetArray = Array.isArray(target) ? target : [];
    const map2 = /* @__PURE__ */ new Map();
    targetArray.forEach((item) => {
      if (item && typeof item === "object" && uniqueProperty in item) {
        map2.set(item[uniqueProperty], item);
      } else {
        map2.set(Symbol(), item);
      }
    });
    source.forEach((item) => {
      if (item && typeof item === "object" && uniqueProperty in item) {
        map2.set(item[uniqueProperty], item);
      } else {
        map2.set(Symbol(), item);
      }
    });
    return Array.from(map2.values());
  }
  async setRememberedState(pageResponse) {
    const rememberedState = await history.getState(history.rememberedState, {});
    if (this.requestParams.all().preserveState && rememberedState && pageResponse.component === page.get().component) {
      pageResponse.rememberedState = rememberedState;
    }
  }
  getScopedErrors(errors) {
    if (!this.requestParams.all().errorBag) {
      return errors;
    }
    return errors[this.requestParams.all().errorBag || ""] || {};
  }
};
var Request = class _Request {
  constructor(params, page2) {
    this.page = page2;
    this.requestHasFinished = false;
    this.requestParams = RequestParams.create(params);
    this.cancelToken = new AbortController();
  }
  static create(params, page2) {
    return new _Request(params, page2);
  }
  async send() {
    this.requestParams.onCancelToken(() => this.cancel({ cancelled: true }));
    fireStartEvent(this.requestParams.all());
    this.requestParams.onStart();
    if (this.requestParams.all().prefetch) {
      this.requestParams.onPrefetching();
      firePrefetchingEvent(this.requestParams.all());
    }
    const originallyPrefetch = this.requestParams.all().prefetch;
    return axios_default({
      method: this.requestParams.all().method,
      url: urlWithoutHash(this.requestParams.all().url).href,
      data: this.requestParams.data(),
      params: this.requestParams.queryParams(),
      signal: this.cancelToken.signal,
      headers: this.getHeaders(),
      onUploadProgress: this.onProgress.bind(this),
      // Why text? This allows us to delay JSON.parse until we're ready to use the response,
      // helps with performance particularly on large responses + history encryption
      responseType: "text"
    }).then((response) => {
      this.response = Response.create(this.requestParams, response, this.page);
      return this.response.handle();
    }).catch((error) => {
      if (error?.response) {
        this.response = Response.create(this.requestParams, error.response, this.page);
        return this.response.handle();
      }
      return Promise.reject(error);
    }).catch((error) => {
      if (axios_default.isCancel(error)) {
        return;
      }
      if (fireExceptionEvent(error)) {
        return Promise.reject(error);
      }
    }).finally(() => {
      this.finish();
      if (originallyPrefetch && this.response) {
        this.requestParams.onPrefetchResponse(this.response);
      }
    });
  }
  finish() {
    if (this.requestParams.wasCancelledAtAll()) {
      return;
    }
    this.requestParams.markAsFinished();
    this.fireFinishEvents();
  }
  fireFinishEvents() {
    if (this.requestHasFinished) {
      return;
    }
    this.requestHasFinished = true;
    fireFinishEvent(this.requestParams.all());
    this.requestParams.onFinish();
  }
  cancel({ cancelled = false, interrupted = false }) {
    if (this.requestHasFinished) {
      return;
    }
    this.cancelToken.abort();
    this.requestParams.markAsCancelled({ cancelled, interrupted });
    this.fireFinishEvents();
  }
  onProgress(progress3) {
    if (this.requestParams.data() instanceof FormData) {
      progress3.percentage = progress3.progress ? Math.round(progress3.progress * 100) : 0;
      fireProgressEvent(progress3);
      this.requestParams.all().onProgress(progress3);
    }
  }
  getHeaders() {
    const headers = {
      ...this.requestParams.headers(),
      Accept: "text/html, application/xhtml+xml",
      "X-Requested-With": "XMLHttpRequest",
      "X-Inertia": true
    };
    if (page.get().version) {
      headers["X-Inertia-Version"] = page.get().version;
    }
    return headers;
  }
};
var RequestStream = class {
  constructor({ maxConcurrent, interruptible }) {
    this.requests = [];
    this.maxConcurrent = maxConcurrent;
    this.interruptible = interruptible;
  }
  send(request) {
    this.requests.push(request);
    request.send().then(() => {
      this.requests = this.requests.filter((r) => r !== request);
    });
  }
  interruptInFlight() {
    this.cancel({ interrupted: true }, false);
  }
  cancelInFlight() {
    this.cancel({ cancelled: true }, true);
  }
  cancel({ cancelled = false, interrupted = false } = {}, force) {
    if (!this.shouldCancel(force)) {
      return;
    }
    const request = this.requests.shift();
    request?.cancel({ interrupted, cancelled });
  }
  shouldCancel(force) {
    if (force) {
      return true;
    }
    return this.interruptible && this.requests.length >= this.maxConcurrent;
  }
};
var Router = class {
  constructor() {
    this.syncRequestStream = new RequestStream({
      maxConcurrent: 1,
      interruptible: true
    });
    this.asyncRequestStream = new RequestStream({
      maxConcurrent: Infinity,
      interruptible: false
    });
  }
  init({ initialPage, resolveComponent, swapComponent: swapComponent2 }) {
    page.init({
      initialPage,
      resolveComponent,
      swapComponent: swapComponent2
    });
    InitialVisit.handle();
    eventHandler.init();
    eventHandler.on("missingHistoryItem", () => {
      if (typeof window !== "undefined") {
        this.visit(window.location.href, { preserveState: true, preserveScroll: true, replace: true });
      }
    });
    eventHandler.on("loadDeferredProps", () => {
      this.loadDeferredProps();
    });
  }
  get(url, data = {}, options = {}) {
    return this.visit(url, { ...options, method: "get", data });
  }
  post(url, data = {}, options = {}) {
    return this.visit(url, { preserveState: true, ...options, method: "post", data });
  }
  put(url, data = {}, options = {}) {
    return this.visit(url, { preserveState: true, ...options, method: "put", data });
  }
  patch(url, data = {}, options = {}) {
    return this.visit(url, { preserveState: true, ...options, method: "patch", data });
  }
  delete(url, options = {}) {
    return this.visit(url, { preserveState: true, ...options, method: "delete" });
  }
  reload(options = {}) {
    if (typeof window === "undefined") {
      return;
    }
    return this.visit(window.location.href, {
      ...options,
      preserveScroll: true,
      preserveState: true,
      async: true,
      headers: {
        ...options.headers || {},
        "Cache-Control": "no-cache"
      }
    });
  }
  remember(data, key = "default") {
    history.remember(data, key);
  }
  restore(key = "default") {
    return history.restore(key);
  }
  on(type, callback) {
    if (typeof window === "undefined") {
      return () => {
      };
    }
    return eventHandler.onGlobalEvent(type, callback);
  }
  cancel() {
    this.syncRequestStream.cancelInFlight();
  }
  cancelAll() {
    this.asyncRequestStream.cancelInFlight();
    this.syncRequestStream.cancelInFlight();
  }
  poll(interval, requestOptions = {}, options = {}) {
    return polls.add(interval, () => this.reload(requestOptions), {
      autoStart: options.autoStart ?? true,
      keepAlive: options.keepAlive ?? false
    });
  }
  visit(href, options = {}) {
    const visit = this.getPendingVisit(href, {
      ...options,
      showProgress: options.showProgress ?? !options.async
    });
    const events = this.getVisitEvents(options);
    if (events.onBefore(visit) === false || !fireBeforeEvent(visit)) {
      return;
    }
    const requestStream = visit.async ? this.asyncRequestStream : this.syncRequestStream;
    requestStream.interruptInFlight();
    if (!page.isCleared() && !visit.preserveUrl) {
      Scroll.save();
    }
    const requestParams = {
      ...visit,
      ...events
    };
    const prefetched = prefetchedRequests.get(requestParams);
    if (prefetched) {
      reveal(prefetched.inFlight);
      prefetchedRequests.use(prefetched, requestParams);
    } else {
      reveal(true);
      requestStream.send(Request.create(requestParams, page.get()));
    }
  }
  getCached(href, options = {}) {
    return prefetchedRequests.findCached(this.getPrefetchParams(href, options));
  }
  flush(href, options = {}) {
    prefetchedRequests.remove(this.getPrefetchParams(href, options));
  }
  flushAll() {
    prefetchedRequests.removeAll();
  }
  getPrefetching(href, options = {}) {
    return prefetchedRequests.findInFlight(this.getPrefetchParams(href, options));
  }
  prefetch(href, options = {}, { cacheFor = 3e4 }) {
    if (options.method !== "get") {
      throw new Error("Prefetch requests must use the GET method");
    }
    const visit = this.getPendingVisit(href, {
      ...options,
      async: true,
      showProgress: false,
      prefetch: true
    });
    const visitUrl = visit.url.origin + visit.url.pathname + visit.url.search;
    const currentUrl = window.location.origin + window.location.pathname + window.location.search;
    if (visitUrl === currentUrl) {
      return;
    }
    const events = this.getVisitEvents(options);
    if (events.onBefore(visit) === false || !fireBeforeEvent(visit)) {
      return;
    }
    hide();
    this.asyncRequestStream.interruptInFlight();
    const requestParams = {
      ...visit,
      ...events
    };
    const ensureCurrentPageIsSet = () => {
      return new Promise((resolve) => {
        const checkIfPageIsDefined = () => {
          if (page.get()) {
            resolve();
          } else {
            setTimeout(checkIfPageIsDefined, 50);
          }
        };
        checkIfPageIsDefined();
      });
    };
    ensureCurrentPageIsSet().then(() => {
      prefetchedRequests.add(
        requestParams,
        (params) => {
          this.asyncRequestStream.send(Request.create(params, page.get()));
        },
        { cacheFor }
      );
    });
  }
  clearHistory() {
    history.clear();
  }
  decryptHistory() {
    return history.decrypt();
  }
  resolveComponent(component) {
    return page.resolve(component);
  }
  replace(params) {
    this.clientVisit(params, { replace: true });
  }
  push(params) {
    this.clientVisit(params);
  }
  clientVisit(params, { replace: replace2 = false } = {}) {
    const current = page.get();
    const props = typeof params.props === "function" ? params.props(current.props) : params.props ?? current.props;
    const { onError, onFinish, onSuccess, ...pageParams } = params;
    page.set(
      {
        ...current,
        ...pageParams,
        props
      },
      {
        replace: replace2,
        preserveScroll: params.preserveScroll,
        preserveState: params.preserveState
      }
    ).then(() => {
      const errors = page.get().props.errors || {};
      if (Object.keys(errors).length === 0) {
        return onSuccess?.(page.get());
      }
      const scopedErrors = params.errorBag ? errors[params.errorBag || ""] || {} : errors;
      return onError?.(scopedErrors);
    }).finally(() => onFinish?.(params));
  }
  getPrefetchParams(href, options) {
    return {
      ...this.getPendingVisit(href, {
        ...options,
        async: true,
        showProgress: false,
        prefetch: true
      }),
      ...this.getVisitEvents(options)
    };
  }
  getPendingVisit(href, options, pendingVisitOptions = {}) {
    const mergedOptions = {
      method: "get",
      data: {},
      replace: false,
      preserveScroll: false,
      preserveState: false,
      only: [],
      except: [],
      headers: {},
      errorBag: "",
      forceFormData: false,
      queryStringArrayFormat: "brackets",
      async: false,
      showProgress: true,
      fresh: false,
      reset: [],
      preserveUrl: false,
      prefetch: false,
      ...options
    };
    const [url, _data] = transformUrlAndData(
      href,
      mergedOptions.data,
      mergedOptions.method,
      mergedOptions.forceFormData,
      mergedOptions.queryStringArrayFormat
    );
    const visit = {
      cancelled: false,
      completed: false,
      interrupted: false,
      ...mergedOptions,
      ...pendingVisitOptions,
      url,
      data: _data
    };
    if (visit.prefetch) {
      visit.headers["Purpose"] = "prefetch";
    }
    return visit;
  }
  getVisitEvents(options) {
    return {
      onCancelToken: options.onCancelToken || (() => {
      }),
      onBefore: options.onBefore || (() => {
      }),
      onStart: options.onStart || (() => {
      }),
      onProgress: options.onProgress || (() => {
      }),
      onFinish: options.onFinish || (() => {
      }),
      onCancel: options.onCancel || (() => {
      }),
      onSuccess: options.onSuccess || (() => {
      }),
      onError: options.onError || (() => {
      }),
      onPrefetched: options.onPrefetched || (() => {
      }),
      onPrefetching: options.onPrefetching || (() => {
      })
    };
  }
  loadDeferredProps() {
    const deferred = page.get()?.deferredProps;
    if (deferred) {
      Object.entries(deferred).forEach(([_, group]) => {
        this.reload({ only: group });
      });
    }
  }
};
var Renderer = {
  buildDOMElement(tag) {
    const template2 = document.createElement("template");
    template2.innerHTML = tag;
    const node = template2.content.firstChild;
    if (!tag.startsWith("<script ")) {
      return node;
    }
    const script = document.createElement("script");
    script.innerHTML = node.innerHTML;
    node.getAttributeNames().forEach((name) => {
      script.setAttribute(name, node.getAttribute(name) || "");
    });
    return script;
  },
  isInertiaManagedElement(element) {
    return element.nodeType === Node.ELEMENT_NODE && element.getAttribute("inertia") !== null;
  },
  findMatchingElementIndex(element, elements) {
    const key = element.getAttribute("inertia");
    if (key !== null) {
      return elements.findIndex((element2) => element2.getAttribute("inertia") === key);
    }
    return -1;
  },
  update: debounce2(function(elements) {
    const sourceElements = elements.map((element) => this.buildDOMElement(element));
    const targetElements = Array.from(document.head.childNodes).filter(
      (element) => this.isInertiaManagedElement(element)
    );
    targetElements.forEach((targetElement) => {
      const index = this.findMatchingElementIndex(targetElement, sourceElements);
      if (index === -1) {
        targetElement?.parentNode?.removeChild(targetElement);
        return;
      }
      const sourceElement = sourceElements.splice(index, 1)[0];
      if (sourceElement && !targetElement.isEqualNode(sourceElement)) {
        targetElement?.parentNode?.replaceChild(sourceElement, targetElement);
      }
    });
    sourceElements.forEach((element) => document.head.appendChild(element));
  }, 1)
};
function createHeadManager(isServer2, titleCallback, onUpdate) {
  const states = {};
  let lastProviderId = 0;
  function connect() {
    const id = lastProviderId += 1;
    states[id] = [];
    return id.toString();
  }
  function disconnect(id) {
    if (id === null || Object.keys(states).indexOf(id) === -1) {
      return;
    }
    delete states[id];
    commit();
  }
  function reconnect(id) {
    if (Object.keys(states).indexOf(id) === -1) {
      states[id] = [];
    }
  }
  function update2(id, elements = []) {
    if (id !== null && Object.keys(states).indexOf(id) > -1) {
      states[id] = elements;
    }
    commit();
  }
  function collect() {
    const title = titleCallback("");
    const defaults2 = {
      ...title ? { title: `<title inertia="">${title}</title>` } : {}
    };
    const elements = Object.values(states).reduce((carry, elements2) => carry.concat(elements2), []).reduce((carry, element) => {
      if (element.indexOf("<") === -1) {
        return carry;
      }
      if (element.indexOf("<title ") === 0) {
        const title2 = element.match(/(<title [^>]+>)(.*?)(<\/title>)/);
        carry.title = title2 ? `${title2[1]}${titleCallback(title2[2])}${title2[3]}` : element;
        return carry;
      }
      const match = element.match(/ inertia="[^"]+"/);
      if (match) {
        carry[match[0]] = element;
      } else {
        carry[Object.keys(carry).length] = element;
      }
      return carry;
    }, defaults2);
    return Object.values(elements);
  }
  function commit() {
    isServer2 ? onUpdate(collect()) : Renderer.update(collect());
  }
  commit();
  return {
    forceUpdate: commit,
    createProvider: function() {
      const id = connect();
      return {
        reconnect: () => reconnect(id),
        update: (elements) => update2(id, elements),
        disconnect: () => disconnect(id)
      };
    }
  };
}
var baseComponentSelector = "nprogress";
var progress;
var settings = {
  minimum: 0.08,
  easing: "linear",
  positionUsing: "translate3d",
  speed: 200,
  trickle: true,
  trickleSpeed: 200,
  showSpinner: true,
  barSelector: '[role="bar"]',
  spinnerSelector: '[role="spinner"]',
  parent: "body",
  color: "#29d",
  includeCSS: true,
  template: [
    '<div class="bar" role="bar">',
    '<div class="peg"></div>',
    "</div>",
    '<div class="spinner" role="spinner">',
    '<div class="spinner-icon"></div>',
    "</div>"
  ].join("")
};
var status = null;
var configure = (options) => {
  Object.assign(settings, options);
  if (settings.includeCSS) {
    injectCSS(settings.color);
  }
  progress = document.createElement("div");
  progress.id = baseComponentSelector;
  progress.innerHTML = settings.template;
};
var set = (n) => {
  const started = isStarted();
  n = clamp2(n, settings.minimum, 1);
  status = n === 1 ? null : n;
  const progress3 = render(!started);
  const bar = progress3.querySelector(settings.barSelector);
  const speed = settings.speed;
  const ease = settings.easing;
  progress3.offsetWidth;
  queue3((next) => {
    const barStyles = (() => {
      if (settings.positionUsing === "translate3d") {
        return {
          transition: `all ${speed}ms ${ease}`,
          transform: `translate3d(${toBarPercentage(n)}%,0,0)`
        };
      }
      if (settings.positionUsing === "translate") {
        return {
          transition: `all ${speed}ms ${ease}`,
          transform: `translate(${toBarPercentage(n)}%,0)`
        };
      }
      return { marginLeft: `${toBarPercentage(n)}%` };
    })();
    for (const key in barStyles) {
      bar.style[key] = barStyles[key];
    }
    if (n !== 1) {
      return setTimeout(next, speed);
    }
    progress3.style.transition = "none";
    progress3.style.opacity = "1";
    progress3.offsetWidth;
    setTimeout(() => {
      progress3.style.transition = `all ${speed}ms linear`;
      progress3.style.opacity = "0";
      setTimeout(() => {
        remove2();
        progress3.style.transition = "";
        progress3.style.opacity = "";
        next();
      }, speed);
    }, speed);
  });
};
var isStarted = () => typeof status === "number";
var start = () => {
  if (!status) {
    set(0);
  }
  const work = function() {
    setTimeout(function() {
      if (!status) {
        return;
      }
      increaseByRandom();
      work();
    }, settings.trickleSpeed);
  };
  if (settings.trickle) {
    work();
  }
};
var done = (force) => {
  if (!force && !status) {
    return;
  }
  increaseByRandom(0.3 + 0.5 * Math.random());
  set(1);
};
var increaseByRandom = (amount) => {
  const n = status;
  if (n === null) {
    return start();
  }
  if (n > 1) {
    return;
  }
  amount = typeof amount === "number" ? amount : (() => {
    const ranges = {
      0.1: [0, 0.2],
      0.04: [0.2, 0.5],
      0.02: [0.5, 0.8],
      5e-3: [0.8, 0.99]
    };
    for (const r in ranges) {
      if (n >= ranges[r][0] && n < ranges[r][1]) {
        return parseFloat(r);
      }
    }
    return 0;
  })();
  return set(clamp2(n + amount, 0, 0.994));
};
var render = (fromStart) => {
  if (isRendered()) {
    return document.getElementById(baseComponentSelector);
  }
  document.documentElement.classList.add(`${baseComponentSelector}-busy`);
  const bar = progress.querySelector(settings.barSelector);
  const perc = fromStart ? "-100" : toBarPercentage(status || 0);
  const parent = getParent();
  bar.style.transition = "all 0 linear";
  bar.style.transform = `translate3d(${perc}%,0,0)`;
  if (!settings.showSpinner) {
    progress.querySelector(settings.spinnerSelector)?.remove();
  }
  if (parent !== document.body) {
    parent.classList.add(`${baseComponentSelector}-custom-parent`);
  }
  parent.appendChild(progress);
  return progress;
};
var getParent = () => {
  return isDOM(settings.parent) ? settings.parent : document.querySelector(settings.parent);
};
var remove2 = () => {
  document.documentElement.classList.remove(`${baseComponentSelector}-busy`);
  getParent().classList.remove(`${baseComponentSelector}-custom-parent`);
  progress?.remove();
};
var isRendered = () => {
  return document.getElementById(baseComponentSelector) !== null;
};
var isDOM = (obj) => {
  if (typeof HTMLElement === "object") {
    return obj instanceof HTMLElement;
  }
  return obj && typeof obj === "object" && obj.nodeType === 1 && typeof obj.nodeName === "string";
};
function clamp2(n, min2, max2) {
  if (n < min2) {
    return min2;
  }
  if (n > max2) {
    return max2;
  }
  return n;
}
var toBarPercentage = (n) => (-1 + n) * 100;
var queue3 = /* @__PURE__ */ (() => {
  const pending = [];
  const next = () => {
    const fn = pending.shift();
    if (fn) {
      fn(next);
    }
  };
  return (fn) => {
    pending.push(fn);
    if (pending.length === 1) {
      next();
    }
  };
})();
var injectCSS = (color) => {
  const element = document.createElement("style");
  element.textContent = `
    #${baseComponentSelector} {
      pointer-events: none;
    }

    #${baseComponentSelector} .bar {
      background: ${color};

      position: fixed;
      z-index: 1031;
      top: 0;
      left: 0;

      width: 100%;
      height: 2px;
    }

    #${baseComponentSelector} .peg {
      display: block;
      position: absolute;
      right: 0px;
      width: 100px;
      height: 100%;
      box-shadow: 0 0 10px ${color}, 0 0 5px ${color};
      opacity: 1.0;

      transform: rotate(3deg) translate(0px, -4px);
    }

    #${baseComponentSelector} .spinner {
      display: block;
      position: fixed;
      z-index: 1031;
      top: 15px;
      right: 15px;
    }

    #${baseComponentSelector} .spinner-icon {
      width: 18px;
      height: 18px;
      box-sizing: border-box;

      border: solid 2px transparent;
      border-top-color: ${color};
      border-left-color: ${color};
      border-radius: 50%;

      animation: ${baseComponentSelector}-spinner 400ms linear infinite;
    }

    .${baseComponentSelector}-custom-parent {
      overflow: hidden;
      position: relative;
    }

    .${baseComponentSelector}-custom-parent #${baseComponentSelector} .spinner,
    .${baseComponentSelector}-custom-parent #${baseComponentSelector} .bar {
      position: absolute;
    }

    @keyframes ${baseComponentSelector}-spinner {
      0%   { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;
  document.head.appendChild(element);
};
var show = () => {
  if (progress) {
    progress.style.display = "";
  }
};
var hide2 = () => {
  if (progress) {
    progress.style.display = "none";
  }
};
var progress_component_default = {
  configure,
  isStarted,
  done,
  set,
  remove: remove2,
  start,
  status,
  show,
  hide: hide2
};
var hideCount = 0;
var reveal = (force = false) => {
  hideCount = Math.max(0, hideCount - 1);
  if (force || hideCount === 0) {
    progress_component_default.show();
  }
};
var hide = () => {
  hideCount++;
  progress_component_default.hide();
};
function addEventListeners(delay3) {
  document.addEventListener("inertia:start", (e) => start2(e, delay3));
  document.addEventListener("inertia:progress", progress2);
}
function start2(event, delay3) {
  if (!event.detail.visit.showProgress) {
    hide();
  }
  const timeout2 = setTimeout(() => progress_component_default.start(), delay3);
  document.addEventListener("inertia:finish", (e) => finish(e, timeout2), { once: true });
}
function progress2(event) {
  if (progress_component_default.isStarted() && event.detail.progress?.percentage) {
    progress_component_default.set(Math.max(progress_component_default.status, event.detail.progress.percentage / 100 * 0.9));
  }
}
function finish(event, timeout2) {
  clearTimeout(timeout2);
  if (!progress_component_default.isStarted()) {
    return;
  }
  if (event.detail.visit.completed) {
    progress_component_default.done();
  } else if (event.detail.visit.interrupted) {
    progress_component_default.set(0);
  } else if (event.detail.visit.cancelled) {
    progress_component_default.done();
    progress_component_default.remove();
  }
}
function setupProgress({
  delay: delay3 = 250,
  color = "#29d",
  includeCSS = true,
  showSpinner = false
} = {}) {
  addEventListeners(delay3);
  progress_component_default.configure({ showSpinner, includeCSS, color });
}
function shouldIntercept(event) {
  const isLink = event.currentTarget.tagName.toLowerCase() === "a";
  return !(event.target && (event?.target).isContentEditable || event.defaultPrevented || isLink && event.altKey || isLink && event.ctrlKey || isLink && event.metaKey || isLink && event.shiftKey || isLink && "button" in event && event.button !== 0);
}
var router = new Router();

// node_modules/@inertiajs/react/dist/index.esm.js
var import_react = __toESM(require_react());
var import_react2 = __toESM(require_react());
var import_react3 = __toESM(require_react());
var import_react4 = __toESM(require_react());
var import_react5 = __toESM(require_react());
var import_react6 = __toESM(require_react());
var import_react7 = __toESM(require_react());
var import_react8 = __toESM(require_react());

// node_modules/es-toolkit/dist/compat/array/castArray.mjs
function castArray(value) {
  if (arguments.length === 0) {
    return [];
  }
  return Array.isArray(value) ? value : [value];
}

// node_modules/es-toolkit/dist/compat/_internal/toArray.mjs
function toArray(value) {
  return Array.isArray(value) ? value : Array.from(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isArrayLike.mjs
function isArrayLike(value) {
  return value != null && typeof value !== "function" && isLength(value.length);
}

// node_modules/es-toolkit/dist/compat/array/chunk.mjs
function chunk2(arr, size2 = 1) {
  size2 = Math.max(Math.floor(size2), 0);
  if (size2 === 0 || !isArrayLike(arr)) {
    return [];
  }
  return chunk(toArray(arr), size2);
}

// node_modules/es-toolkit/dist/compat/array/compact.mjs
function compact2(arr) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return compact(Array.from(arr));
}

// node_modules/es-toolkit/dist/compat/array/concat.mjs
function concat(...values2) {
  return flatten(values2);
}

// node_modules/es-toolkit/dist/compat/_internal/isDeepKey.mjs
function isDeepKey(key) {
  switch (typeof key) {
    case "number":
    case "symbol": {
      return false;
    }
    case "string": {
      return key.includes(".") || key.includes("[") || key.includes("]");
    }
  }
}

// node_modules/es-toolkit/dist/compat/_internal/toKey.mjs
function toKey(value) {
  if (typeof value === "string" || typeof value === "symbol") {
    return value;
  }
  if (Object.is(value?.valueOf?.(), -0)) {
    return "-0";
  }
  return String(value);
}

// node_modules/es-toolkit/dist/compat/util/toPath.mjs
function toPath(deepKey) {
  const result2 = [];
  const length = deepKey.length;
  if (length === 0) {
    return result2;
  }
  let index = 0;
  let key = "";
  let quoteChar = "";
  let bracket = false;
  if (deepKey.charCodeAt(0) === 46) {
    result2.push("");
    index++;
  }
  while (index < length) {
    const char = deepKey[index];
    if (quoteChar) {
      if (char === "\\" && index + 1 < length) {
        index++;
        key += deepKey[index];
      } else if (char === quoteChar) {
        quoteChar = "";
      } else {
        key += char;
      }
    } else if (bracket) {
      if (char === '"' || char === "'") {
        quoteChar = char;
      } else if (char === "]") {
        bracket = false;
        result2.push(key);
        key = "";
      } else {
        key += char;
      }
    } else {
      if (char === "[") {
        bracket = true;
        if (key) {
          result2.push(key);
          key = "";
        }
      } else if (char === ".") {
        if (key) {
          result2.push(key);
          key = "";
        }
      } else {
        key += char;
      }
    }
    index++;
  }
  if (key) {
    result2.push(key);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/get.mjs
function get(object, path, defaultValue) {
  if (object == null) {
    return defaultValue;
  }
  switch (typeof path) {
    case "string": {
      if (isUnsafeProperty(path)) {
        return defaultValue;
      }
      const result2 = object[path];
      if (result2 === void 0) {
        if (isDeepKey(path)) {
          return get(object, toPath(path), defaultValue);
        } else {
          return defaultValue;
        }
      }
      return result2;
    }
    case "number":
    case "symbol": {
      if (typeof path === "number") {
        path = toKey(path);
      }
      const result2 = object[path];
      if (result2 === void 0) {
        return defaultValue;
      }
      return result2;
    }
    default: {
      if (Array.isArray(path)) {
        return getWithPath(object, path, defaultValue);
      }
      if (Object.is(path?.valueOf(), -0)) {
        path = "-0";
      } else {
        path = String(path);
      }
      if (isUnsafeProperty(path)) {
        return defaultValue;
      }
      const result2 = object[path];
      if (result2 === void 0) {
        return defaultValue;
      }
      return result2;
    }
  }
}
function getWithPath(object, path, defaultValue) {
  if (path.length === 0) {
    return defaultValue;
  }
  let current = object;
  for (let index = 0; index < path.length; index++) {
    if (current == null) {
      return defaultValue;
    }
    if (isUnsafeProperty(path[index])) {
      return defaultValue;
    }
    current = current[path[index]];
  }
  if (current === void 0) {
    return defaultValue;
  }
  return current;
}

// node_modules/es-toolkit/dist/compat/object/property.mjs
function property(path) {
  return function(object) {
    return get(object, path);
  };
}

// node_modules/es-toolkit/dist/compat/predicate/isObject.mjs
function isObject(value) {
  return value !== null && (typeof value === "object" || typeof value === "function");
}

// node_modules/es-toolkit/dist/compat/predicate/isMatchWith.mjs
function isMatchWith(target, source, compare) {
  if (typeof compare !== "function") {
    return isMatch(target, source);
  }
  return isMatchWithInternal(target, source, function doesMatch(objValue, srcValue, key, object, source2, stack) {
    const isEqual2 = compare(objValue, srcValue, key, object, source2, stack);
    if (isEqual2 !== void 0) {
      return Boolean(isEqual2);
    }
    return isMatchWithInternal(objValue, srcValue, doesMatch, stack);
  }, /* @__PURE__ */ new Map());
}
function isMatchWithInternal(target, source, compare, stack) {
  if (source === target) {
    return true;
  }
  switch (typeof source) {
    case "object": {
      return isObjectMatch(target, source, compare, stack);
    }
    case "function": {
      const sourceKeys = Object.keys(source);
      if (sourceKeys.length > 0) {
        return isMatchWithInternal(target, { ...source }, compare, stack);
      }
      return eq(target, source);
    }
    default: {
      if (!isObject(target)) {
        return eq(target, source);
      }
      if (typeof source === "string") {
        return source === "";
      }
      return true;
    }
  }
}
function isObjectMatch(target, source, compare, stack) {
  if (source == null) {
    return true;
  }
  if (Array.isArray(source)) {
    return isArrayMatch(target, source, compare, stack);
  }
  if (source instanceof Map) {
    return isMapMatch(target, source, compare, stack);
  }
  if (source instanceof Set) {
    return isSetMatch(target, source, compare, stack);
  }
  const keys2 = Object.keys(source);
  if (target == null) {
    return keys2.length === 0;
  }
  if (keys2.length === 0) {
    return true;
  }
  if (stack && stack.has(source)) {
    return stack.get(source) === target;
  }
  if (stack) {
    stack.set(source, target);
  }
  try {
    for (let i = 0; i < keys2.length; i++) {
      const key = keys2[i];
      if (!isPrimitive(target) && !(key in target)) {
        return false;
      }
      if (source[key] === void 0 && target[key] !== void 0) {
        return false;
      }
      if (source[key] === null && target[key] !== null) {
        return false;
      }
      const isEqual2 = compare(target[key], source[key], key, target, source, stack);
      if (!isEqual2) {
        return false;
      }
    }
    return true;
  } finally {
    if (stack) {
      stack.delete(source);
    }
  }
}
function isMapMatch(target, source, compare, stack) {
  if (source.size === 0) {
    return true;
  }
  if (!(target instanceof Map)) {
    return false;
  }
  for (const [key, sourceValue] of source.entries()) {
    const targetValue = target.get(key);
    const isEqual2 = compare(targetValue, sourceValue, key, target, source, stack);
    if (isEqual2 === false) {
      return false;
    }
  }
  return true;
}
function isArrayMatch(target, source, compare, stack) {
  if (source.length === 0) {
    return true;
  }
  if (!Array.isArray(target)) {
    return false;
  }
  const countedIndex = /* @__PURE__ */ new Set();
  for (let i = 0; i < source.length; i++) {
    const sourceItem = source[i];
    let found = false;
    for (let j = 0; j < target.length; j++) {
      if (countedIndex.has(j)) {
        continue;
      }
      const targetItem = target[j];
      let matches2 = false;
      const isEqual2 = compare(targetItem, sourceItem, i, target, source, stack);
      if (isEqual2) {
        matches2 = true;
      }
      if (matches2) {
        countedIndex.add(j);
        found = true;
        break;
      }
    }
    if (!found) {
      return false;
    }
  }
  return true;
}
function isSetMatch(target, source, compare, stack) {
  if (source.size === 0) {
    return true;
  }
  if (!(target instanceof Set)) {
    return false;
  }
  return isArrayMatch([...target], [...source], compare, stack);
}

// node_modules/es-toolkit/dist/compat/predicate/isMatch.mjs
function isMatch(target, source) {
  return isMatchWith(target, source, () => void 0);
}

// node_modules/es-toolkit/dist/compat/predicate/matches.mjs
function matches(source) {
  source = cloneDeep(source);
  return (target) => {
    return isMatch(target, source);
  };
}

// node_modules/es-toolkit/dist/compat/object/cloneDeepWith.mjs
function cloneDeepWith2(obj, customizer) {
  return cloneDeepWith(obj, (value, key, object, stack) => {
    const cloned = customizer?.(value, key, object, stack);
    if (cloned != null) {
      return cloned;
    }
    if (typeof obj !== "object") {
      return void 0;
    }
    switch (Object.prototype.toString.call(obj)) {
      case numberTag:
      case stringTag:
      case booleanTag: {
        const result2 = new obj.constructor(obj?.valueOf());
        copyProperties(result2, obj);
        return result2;
      }
      case argumentsTag: {
        const result2 = {};
        copyProperties(result2, obj);
        result2.length = obj.length;
        result2[Symbol.iterator] = obj[Symbol.iterator];
        return result2;
      }
      default: {
        return void 0;
      }
    }
  });
}

// node_modules/es-toolkit/dist/compat/object/cloneDeep.mjs
function cloneDeep2(obj) {
  return cloneDeepWith2(obj);
}

// node_modules/es-toolkit/dist/compat/_internal/isIndex.mjs
var IS_UNSIGNED_INTEGER = /^(?:0|[1-9]\d*)$/;
function isIndex(value, length = Number.MAX_SAFE_INTEGER) {
  switch (typeof value) {
    case "number": {
      return Number.isInteger(value) && value >= 0 && value < length;
    }
    case "symbol": {
      return false;
    }
    case "string": {
      return IS_UNSIGNED_INTEGER.test(value);
    }
  }
}

// node_modules/es-toolkit/dist/compat/predicate/isArguments.mjs
function isArguments(value) {
  return value !== null && typeof value === "object" && getTag(value) === "[object Arguments]";
}

// node_modules/es-toolkit/dist/compat/object/has.mjs
function has(object, path) {
  let resolvedPath;
  if (Array.isArray(path)) {
    resolvedPath = path;
  } else if (typeof path === "string" && isDeepKey(path) && object?.[path] == null) {
    resolvedPath = toPath(path);
  } else {
    resolvedPath = [path];
  }
  if (resolvedPath.length === 0) {
    return false;
  }
  let current = object;
  for (let i = 0; i < resolvedPath.length; i++) {
    const key = resolvedPath[i];
    if (current == null || !Object.hasOwn(current, key)) {
      const isSparseIndex = (Array.isArray(current) || isArguments(current)) && isIndex(key) && key < current.length;
      if (!isSparseIndex) {
        return false;
      }
    }
    current = current[key];
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/predicate/matchesProperty.mjs
function matchesProperty(property2, source) {
  switch (typeof property2) {
    case "object": {
      if (Object.is(property2?.valueOf(), -0)) {
        property2 = "-0";
      }
      break;
    }
    case "number": {
      property2 = toKey(property2);
      break;
    }
  }
  source = cloneDeep2(source);
  return function(target) {
    const result2 = get(target, property2);
    if (result2 === void 0) {
      return has(target, property2);
    }
    if (source === void 0) {
      return result2 === void 0;
    }
    return isMatch(result2, source);
  };
}

// node_modules/es-toolkit/dist/compat/util/iteratee.mjs
function iteratee(value) {
  if (value == null) {
    return identity;
  }
  switch (typeof value) {
    case "function": {
      return value;
    }
    case "object": {
      if (Array.isArray(value) && value.length === 2) {
        return matchesProperty(value[0], value[1]);
      }
      return matches(value);
    }
    case "string":
    case "symbol":
    case "number": {
      return property(value);
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/countBy.mjs
function countBy2(collection, iteratee$1) {
  if (collection == null) {
    return {};
  }
  const array = isArrayLike(collection) ? Array.from(collection) : Object.values(collection);
  const mapper = iteratee(iteratee$1 ?? void 0);
  const result2 = /* @__PURE__ */ Object.create(null);
  for (let i = 0; i < array.length; i++) {
    const item = array[i];
    const key = mapper(item);
    result2[key] = (result2[key] ?? 0) + 1;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/predicate/isArrayLikeObject.mjs
function isArrayLikeObject(value) {
  return isObjectLike(value) && isArrayLike(value);
}

// node_modules/es-toolkit/dist/compat/array/difference.mjs
function difference2(arr, ...values2) {
  if (!isArrayLikeObject(arr)) {
    return [];
  }
  const arr1 = toArray(arr);
  const arr2 = [];
  for (let i = 0; i < values2.length; i++) {
    const value = values2[i];
    if (isArrayLikeObject(value)) {
      arr2.push(...Array.from(value));
    }
  }
  return difference(arr1, arr2);
}

// node_modules/es-toolkit/dist/compat/array/last.mjs
function last2(array) {
  if (!isArrayLike(array)) {
    return void 0;
  }
  return last(toArray(array));
}

// node_modules/es-toolkit/dist/compat/_internal/flattenArrayLike.mjs
function flattenArrayLike(values2) {
  const result2 = [];
  for (let i = 0; i < values2.length; i++) {
    const arrayLike = values2[i];
    if (!isArrayLikeObject(arrayLike)) {
      continue;
    }
    for (let j = 0; j < arrayLike.length; j++) {
      result2.push(arrayLike[j]);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/differenceBy.mjs
function differenceBy2(arr, ..._values) {
  if (!isArrayLikeObject(arr)) {
    return [];
  }
  const iteratee$1 = last2(_values);
  const values2 = flattenArrayLike(_values);
  if (isArrayLikeObject(iteratee$1)) {
    return difference(Array.from(arr), values2);
  }
  return differenceBy(Array.from(arr), values2, iteratee(iteratee$1));
}

// node_modules/es-toolkit/dist/compat/array/differenceWith.mjs
function differenceWith2(array, ...values2) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  const comparator = last2(values2);
  const flattenedValues = flattenArrayLike(values2);
  if (typeof comparator === "function") {
    return differenceWith(Array.from(array), flattenedValues, comparator);
  }
  return difference(Array.from(array), flattenedValues);
}

// node_modules/es-toolkit/dist/compat/array/drop.mjs
function drop2(collection, itemsCount = 1, guard) {
  if (!isArrayLike(collection)) {
    return [];
  }
  itemsCount = guard ? 1 : toInteger(itemsCount);
  return drop(toArray(collection), itemsCount);
}

// node_modules/es-toolkit/dist/compat/array/dropRight.mjs
function dropRight2(collection, itemsCount = 1, guard) {
  if (!isArrayLike(collection)) {
    return [];
  }
  itemsCount = guard ? 1 : toInteger(itemsCount);
  return dropRight(toArray(collection), itemsCount);
}

// node_modules/es-toolkit/dist/compat/array/dropRightWhile.mjs
function dropRightWhile2(arr, predicate = identity) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return dropRightWhileImpl(Array.from(arr), predicate);
}
function dropRightWhileImpl(arr, predicate) {
  switch (typeof predicate) {
    case "function": {
      return dropRightWhile(arr, (item, index, arr2) => Boolean(predicate(item, index, arr2)));
    }
    case "object": {
      if (Array.isArray(predicate) && predicate.length === 2) {
        const key = predicate[0];
        const value = predicate[1];
        return dropRightWhile(arr, matchesProperty(key, value));
      } else {
        return dropRightWhile(arr, matches(predicate));
      }
    }
    case "symbol":
    case "number":
    case "string": {
      return dropRightWhile(arr, property(predicate));
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/dropWhile.mjs
function dropWhile2(arr, predicate = identity) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return dropWhileImpl(toArray(arr), predicate);
}
function dropWhileImpl(arr, predicate) {
  switch (typeof predicate) {
    case "function": {
      return dropWhile(arr, (item, index, arr2) => Boolean(predicate(item, index, arr2)));
    }
    case "object": {
      if (Array.isArray(predicate) && predicate.length === 2) {
        const key = predicate[0];
        const value = predicate[1];
        return dropWhile(arr, matchesProperty(key, value));
      } else {
        return dropWhile(arr, matches(predicate));
      }
    }
    case "number":
    case "symbol":
    case "string": {
      return dropWhile(arr, property(predicate));
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/forEach.mjs
function forEach(collection, callback = identity) {
  if (!collection) {
    return collection;
  }
  const keys2 = isArrayLike(collection) || Array.isArray(collection) ? range(0, collection.length) : Object.keys(collection);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = collection[key];
    const result2 = callback(value, key, collection);
    if (result2 === false) {
      break;
    }
  }
  return collection;
}

// node_modules/es-toolkit/dist/compat/array/forEachRight.mjs
function forEachRight2(collection, callback = identity) {
  if (!collection) {
    return collection;
  }
  const keys2 = isArrayLike(collection) ? range(0, collection.length) : Object.keys(collection);
  for (let i = keys2.length - 1; i >= 0; i--) {
    const key = keys2[i];
    const value = collection[key];
    const result2 = callback(value, key, collection);
    if (result2 === false) {
      break;
    }
  }
  return collection;
}

// node_modules/es-toolkit/dist/compat/_internal/isIterateeCall.mjs
function isIterateeCall(value, index, object) {
  if (!isObject(object)) {
    return false;
  }
  if (typeof index === "number" && isArrayLike(object) && isIndex(index) && index < object.length || typeof index === "string" && index in object) {
    return eq(object[index], value);
  }
  return false;
}

// node_modules/es-toolkit/dist/compat/array/every.mjs
function every(source, doesMatch, guard) {
  if (!source) {
    return true;
  }
  if (guard && isIterateeCall(source, doesMatch, guard)) {
    doesMatch = void 0;
  }
  if (!doesMatch) {
    doesMatch = identity;
  }
  let predicate;
  switch (typeof doesMatch) {
    case "function": {
      predicate = doesMatch;
      break;
    }
    case "object": {
      if (Array.isArray(doesMatch) && doesMatch.length === 2) {
        const key = doesMatch[0];
        const value = doesMatch[1];
        predicate = matchesProperty(key, value);
      } else {
        predicate = matches(doesMatch);
      }
      break;
    }
    case "symbol":
    case "number":
    case "string": {
      predicate = property(doesMatch);
    }
  }
  if (!isArrayLike(source)) {
    const keys2 = Object.keys(source);
    for (let i = 0; i < keys2.length; i++) {
      const key = keys2[i];
      const value = source[key];
      if (!predicate(value, key, source)) {
        return false;
      }
    }
    return true;
  }
  for (let i = 0; i < source.length; i++) {
    if (!predicate(source[i], i, source)) {
      return false;
    }
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/predicate/isString.mjs
function isString2(value) {
  return typeof value === "string" || value instanceof String;
}

// node_modules/es-toolkit/dist/compat/array/fill.mjs
function fill2(array, value, start3 = 0, end = array ? array.length : 0) {
  if (!isArrayLike(array)) {
    return [];
  }
  if (isString2(array)) {
    return array;
  }
  start3 = Math.floor(start3);
  end = Math.floor(end);
  if (!start3) {
    start3 = 0;
  }
  if (!end) {
    end = 0;
  }
  return fill(array, value, start3, end);
}

// node_modules/es-toolkit/dist/compat/array/filter.mjs
function filter(source, predicate = identity) {
  if (!source) {
    return [];
  }
  predicate = iteratee(predicate);
  if (!Array.isArray(source)) {
    const result3 = [];
    const keys2 = Object.keys(source);
    const length2 = isArrayLike(source) ? source.length : keys2.length;
    for (let i = 0; i < length2; i++) {
      const key = keys2[i];
      const value = source[key];
      if (predicate(value, key, source)) {
        result3.push(value);
      }
    }
    return result3;
  }
  const result2 = [];
  const length = source.length;
  for (let i = 0; i < length; i++) {
    const value = source[i];
    if (predicate(value, i, source)) {
      result2.push(value);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/find.mjs
function find(source, _doesMatch = identity, fromIndex = 0) {
  if (!source) {
    return void 0;
  }
  if (fromIndex < 0) {
    fromIndex = Math.max(source.length + fromIndex, 0);
  }
  const doesMatch = iteratee(_doesMatch);
  if (typeof doesMatch === "function" && !Array.isArray(source)) {
    const keys2 = Object.keys(source);
    for (let i = fromIndex; i < keys2.length; i++) {
      const key = keys2[i];
      const value = source[key];
      if (doesMatch(value, key, source)) {
        return value;
      }
    }
    return void 0;
  }
  const values2 = Array.isArray(source) ? source.slice(fromIndex) : Object.values(source).slice(fromIndex);
  return values2.find(doesMatch);
}

// node_modules/es-toolkit/dist/compat/array/findIndex.mjs
function findIndex(arr, doesMatch, fromIndex = 0) {
  if (!arr) {
    return -1;
  }
  if (fromIndex < 0) {
    fromIndex = Math.max(arr.length + fromIndex, 0);
  }
  const subArray = Array.from(arr).slice(fromIndex);
  let index = -1;
  switch (typeof doesMatch) {
    case "function": {
      index = subArray.findIndex(doesMatch);
      break;
    }
    case "object": {
      if (Array.isArray(doesMatch) && doesMatch.length === 2) {
        const key = doesMatch[0];
        const value = doesMatch[1];
        index = subArray.findIndex(matchesProperty(key, value));
      } else {
        index = subArray.findIndex(matches(doesMatch));
      }
      break;
    }
    case "number":
    case "symbol":
    case "string": {
      index = subArray.findIndex(property(doesMatch));
    }
  }
  return index === -1 ? -1 : index + fromIndex;
}

// node_modules/es-toolkit/dist/compat/array/findLast.mjs
function findLast(source, _doesMatch = identity, fromIndex) {
  if (!source) {
    return void 0;
  }
  const length = Array.isArray(source) ? source.length : Object.keys(source).length;
  fromIndex = toInteger(fromIndex ?? length - 1);
  if (fromIndex < 0) {
    fromIndex = Math.max(length + fromIndex, 0);
  } else {
    fromIndex = Math.min(fromIndex, length - 1);
  }
  const doesMatch = iteratee(_doesMatch);
  if (typeof doesMatch === "function" && !Array.isArray(source)) {
    const keys2 = Object.keys(source);
    for (let i = fromIndex; i >= 0; i--) {
      const key = keys2[i];
      const value = source[key];
      if (doesMatch(value, key, source)) {
        return value;
      }
    }
    return void 0;
  }
  const values2 = Array.isArray(source) ? source.slice(0, fromIndex + 1) : Object.values(source).slice(0, fromIndex + 1);
  return values2.findLast(doesMatch);
}

// node_modules/es-toolkit/dist/compat/array/findLastIndex.mjs
function findLastIndex(arr, doesMatch = identity, fromIndex = arr ? arr.length - 1 : 0) {
  if (!arr) {
    return -1;
  }
  if (fromIndex < 0) {
    fromIndex = Math.max(arr.length + fromIndex, 0);
  } else {
    fromIndex = Math.min(fromIndex, arr.length - 1);
  }
  const subArray = toArray(arr).slice(0, fromIndex + 1);
  switch (typeof doesMatch) {
    case "function": {
      return subArray.findLastIndex(doesMatch);
    }
    case "object": {
      if (Array.isArray(doesMatch) && doesMatch.length === 2) {
        const key = doesMatch[0];
        const value = doesMatch[1];
        return subArray.findLastIndex(matchesProperty(key, value));
      } else {
        return subArray.findLastIndex(matches(doesMatch));
      }
    }
    case "number":
    case "symbol":
    case "string": {
      return subArray.findLastIndex(property(doesMatch));
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/head.mjs
function head2(arr) {
  if (!isArrayLike(arr)) {
    return void 0;
  }
  return head(toArray(arr));
}

// node_modules/es-toolkit/dist/compat/array/flatten.mjs
function flatten2(value, depth = 1) {
  const result2 = [];
  const flooredDepth = Math.floor(depth);
  if (!isArrayLike(value)) {
    return result2;
  }
  const recursive = (arr, currentDepth) => {
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i];
      if (currentDepth < flooredDepth && (Array.isArray(item) || Boolean(item?.[Symbol.isConcatSpreadable]) || item !== null && typeof item === "object" && Object.prototype.toString.call(item) === "[object Arguments]")) {
        if (Array.isArray(item)) {
          recursive(item, currentDepth + 1);
        } else {
          recursive(Array.from(item), currentDepth + 1);
        }
      } else {
        result2.push(item);
      }
    }
  };
  recursive(Array.from(value), 0);
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/flattenDepth.mjs
function flattenDepth(array, depth = 1) {
  return flatten2(array, depth);
}

// node_modules/es-toolkit/dist/compat/array/map.mjs
function map(collection, _iteratee) {
  if (!collection) {
    return [];
  }
  const keys2 = isArrayLike(collection) || Array.isArray(collection) ? range(0, collection.length) : Object.keys(collection);
  const iteratee$1 = iteratee(_iteratee ?? identity);
  const result2 = new Array(keys2.length);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = collection[key];
    result2[i] = iteratee$1(value, key, collection);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/flatMap.mjs
function flatMap2(collection, iteratee2) {
  if (isNil(collection)) {
    return [];
  }
  const mapped = isNil(iteratee2) ? map(collection) : map(collection, iteratee2);
  return flattenDepth(mapped, 1);
}

// node_modules/es-toolkit/dist/compat/array/flatMapDepth.mjs
function flatMapDepth(collection, iteratee$1 = identity, depth = 1) {
  if (collection == null) {
    return [];
  }
  const iterateeFn = iteratee(iteratee$1);
  const mapped = map(collection, iterateeFn);
  return flatten2(mapped, depth);
}

// node_modules/es-toolkit/dist/compat/array/flatMapDeep.mjs
function flatMapDeep2(collection, iteratee2) {
  return flatMapDepth(collection, iteratee2, Infinity);
}

// node_modules/es-toolkit/dist/compat/array/flattenDeep.mjs
function flattenDeep2(value) {
  return flattenDepth(value, Infinity);
}

// node_modules/es-toolkit/dist/compat/array/groupBy.mjs
function groupBy2(source, _getKeyFromItem) {
  if (source == null) {
    return {};
  }
  const items = isArrayLike(source) ? Array.from(source) : Object.values(source);
  const getKeyFromItem = iteratee(_getKeyFromItem ?? identity);
  return groupBy(items, getKeyFromItem);
}

// node_modules/es-toolkit/dist/compat/array/includes.mjs
function includes(source, target, fromIndex, guard) {
  if (source == null) {
    return false;
  }
  if (guard || !fromIndex) {
    fromIndex = 0;
  } else {
    fromIndex = toInteger(fromIndex);
  }
  if (isString2(source)) {
    if (fromIndex > source.length || target instanceof RegExp) {
      return false;
    }
    if (fromIndex < 0) {
      fromIndex = Math.max(0, source.length + fromIndex);
    }
    return source.includes(target, fromIndex);
  }
  if (Array.isArray(source)) {
    return source.includes(target, fromIndex);
  }
  const keys2 = Object.keys(source);
  if (fromIndex < 0) {
    fromIndex = Math.max(0, keys2.length + fromIndex);
  }
  for (let i = fromIndex; i < keys2.length; i++) {
    const value = Reflect.get(source, keys2[i]);
    if (eq(value, target)) {
      return true;
    }
  }
  return false;
}

// node_modules/es-toolkit/dist/compat/array/indexOf.mjs
function indexOf(array, searchElement, fromIndex) {
  if (!isArrayLike(array)) {
    return -1;
  }
  if (Number.isNaN(searchElement)) {
    fromIndex = fromIndex ?? 0;
    if (fromIndex < 0) {
      fromIndex = Math.max(0, array.length + fromIndex);
    }
    for (let i = fromIndex; i < array.length; i++) {
      if (Number.isNaN(array[i])) {
        return i;
      }
    }
    return -1;
  }
  return Array.from(array).indexOf(searchElement, fromIndex);
}

// node_modules/es-toolkit/dist/compat/array/initial.mjs
function initial2(arr) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return initial(Array.from(arr));
}

// node_modules/es-toolkit/dist/compat/array/intersection.mjs
function intersection2(...arrays) {
  if (arrays.length === 0) {
    return [];
  }
  if (!isArrayLikeObject(arrays[0])) {
    return [];
  }
  let result2 = uniq(Array.from(arrays[0]));
  for (let i = 1; i < arrays.length; i++) {
    const array = arrays[i];
    if (!isArrayLikeObject(array)) {
      return [];
    }
    result2 = intersection(result2, Array.from(array));
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/intersectionBy.mjs
function intersectionBy2(array, ...values2) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  const lastValue = last(values2);
  if (lastValue === void 0) {
    return Array.from(array);
  }
  let result2 = uniq(Array.from(array));
  const count = isArrayLikeObject(lastValue) ? values2.length : values2.length - 1;
  for (let i = 0; i < count; ++i) {
    const value = values2[i];
    if (!isArrayLikeObject(value)) {
      return [];
    }
    if (isArrayLikeObject(lastValue)) {
      result2 = intersectionBy(result2, Array.from(value), identity);
    } else if (typeof lastValue === "function") {
      result2 = intersectionBy(result2, Array.from(value), (value2) => lastValue(value2));
    } else if (typeof lastValue === "string") {
      result2 = intersectionBy(result2, Array.from(value), property(lastValue));
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/uniq.mjs
function uniq2(arr) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return uniq(Array.from(arr));
}

// node_modules/es-toolkit/dist/compat/array/intersectionWith.mjs
function intersectionWith2(firstArr, ...otherArrs) {
  if (firstArr == null) {
    return [];
  }
  const _comparator = last2(otherArrs);
  let comparator = eq;
  let uniq$1 = uniq2;
  if (typeof _comparator === "function") {
    comparator = _comparator;
    uniq$1 = uniqPreserve0;
    otherArrs.pop();
  }
  let result2 = uniq$1(Array.from(firstArr));
  for (let i = 0; i < otherArrs.length; ++i) {
    const otherArr = otherArrs[i];
    if (otherArr == null) {
      return [];
    }
    result2 = intersectionWith(result2, Array.from(otherArr), comparator);
  }
  return result2;
}
function uniqPreserve0(arr) {
  const result2 = [];
  const added = /* @__PURE__ */ new Set();
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    if (added.has(item)) {
      continue;
    }
    result2.push(item);
    added.add(item);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/invokeMap.mjs
function invokeMap(collection, path, ...args) {
  if (isNil(collection)) {
    return [];
  }
  const values2 = isArrayLike(collection) ? Array.from(collection) : Object.values(collection);
  const result2 = [];
  for (let i = 0; i < values2.length; i++) {
    const value = values2[i];
    if (isFunction(path)) {
      result2.push(path.apply(value, args));
      continue;
    }
    const method2 = get(value, path);
    let thisContext = value;
    if (Array.isArray(path)) {
      const pathExceptLast = path.slice(0, -1);
      if (pathExceptLast.length > 0) {
        thisContext = get(value, pathExceptLast);
      }
    } else if (typeof path === "string" && path.includes(".")) {
      const parts = path.split(".");
      const pathExceptLast = parts.slice(0, -1).join(".");
      thisContext = get(value, pathExceptLast);
    }
    result2.push(method2 == null ? void 0 : method2.apply(thisContext, args));
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/join.mjs
function join(array, separator) {
  if (!isArrayLike(array)) {
    return "";
  }
  return Array.from(array).join(separator);
}

// node_modules/es-toolkit/dist/compat/array/reduce.mjs
function reduce(collection, iteratee2 = identity, accumulator) {
  if (!collection) {
    return accumulator;
  }
  let keys2;
  let startIndex = 0;
  if (isArrayLike(collection)) {
    keys2 = range(0, collection.length);
    if (accumulator == null && collection.length > 0) {
      accumulator = collection[0];
      startIndex += 1;
    }
  } else {
    keys2 = Object.keys(collection);
    if (accumulator == null) {
      accumulator = collection[keys2[0]];
      startIndex += 1;
    }
  }
  for (let i = startIndex; i < keys2.length; i++) {
    const key = keys2[i];
    const value = collection[key];
    accumulator = iteratee2(accumulator, value, key, collection);
  }
  return accumulator;
}

// node_modules/es-toolkit/dist/compat/array/keyBy.mjs
function keyBy2(collection, iteratee$1) {
  if (!isArrayLike(collection) && !isObjectLike(collection)) {
    return {};
  }
  const keyFn = iteratee(iteratee$1 ?? identity);
  return reduce(collection, (result2, value) => {
    const key = keyFn(value);
    result2[key] = value;
    return result2;
  }, {});
}

// node_modules/es-toolkit/dist/compat/array/lastIndexOf.mjs
function lastIndexOf(array, searchElement, fromIndex) {
  if (!isArrayLike(array) || array.length === 0) {
    return -1;
  }
  const length = array.length;
  let index = fromIndex ?? length - 1;
  if (fromIndex != null) {
    index = index < 0 ? Math.max(length + index, 0) : Math.min(index, length - 1);
  }
  if (Number.isNaN(searchElement)) {
    for (let i = index; i >= 0; i--) {
      if (Number.isNaN(array[i])) {
        return i;
      }
    }
  }
  return Array.from(array).lastIndexOf(searchElement, index);
}

// node_modules/es-toolkit/dist/compat/array/nth.mjs
function nth(array, n = 0) {
  if (!isArrayLikeObject(array) || array.length === 0) {
    return void 0;
  }
  n = toInteger(n);
  if (n < 0) {
    n += array.length;
  }
  return array[n];
}

// node_modules/es-toolkit/dist/compat/_internal/compareValues.mjs
function getPriority(a) {
  if (typeof a === "symbol") {
    return 1;
  }
  if (a === null) {
    return 2;
  }
  if (a === void 0) {
    return 3;
  }
  if (a !== a) {
    return 4;
  }
  return 0;
}
var compareValues3 = (a, b, order) => {
  if (a !== b) {
    const aPriority = getPriority(a);
    const bPriority = getPriority(b);
    if (aPriority === bPriority && aPriority === 0) {
      if (a < b) {
        return order === "desc" ? 1 : -1;
      }
      if (a > b) {
        return order === "desc" ? -1 : 1;
      }
    }
    return order === "desc" ? bPriority - aPriority : aPriority - bPriority;
  }
  return 0;
};

// node_modules/es-toolkit/dist/compat/_internal/isKey.mjs
var regexIsDeepProp = /\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/;
var regexIsPlainProp = /^\w*$/;
function isKey(value, object) {
  if (Array.isArray(value)) {
    return false;
  }
  if (typeof value === "number" || typeof value === "boolean" || value == null || isSymbol(value)) {
    return true;
  }
  return typeof value === "string" && (regexIsPlainProp.test(value) || !regexIsDeepProp.test(value)) || object != null && Object.hasOwn(object, value);
}

// node_modules/es-toolkit/dist/compat/array/orderBy.mjs
function orderBy2(collection, criteria, orders, guard) {
  if (collection == null) {
    return [];
  }
  orders = guard ? void 0 : orders;
  if (!Array.isArray(collection)) {
    collection = Object.values(collection);
  }
  if (!Array.isArray(criteria)) {
    criteria = criteria == null ? [null] : [criteria];
  }
  if (criteria.length === 0) {
    criteria = [null];
  }
  if (!Array.isArray(orders)) {
    orders = orders == null ? [] : [orders];
  }
  orders = orders.map((order) => String(order));
  const getValueByNestedPath = (object, path) => {
    let target = object;
    for (let i = 0; i < path.length && target != null; ++i) {
      target = target[path[i]];
    }
    return target;
  };
  const getValueByCriterion = (criterion, object) => {
    if (object == null || criterion == null) {
      return object;
    }
    if (typeof criterion === "object" && "key" in criterion) {
      if (Object.hasOwn(object, criterion.key)) {
        return object[criterion.key];
      }
      return getValueByNestedPath(object, criterion.path);
    }
    if (typeof criterion === "function") {
      return criterion(object);
    }
    if (Array.isArray(criterion)) {
      return getValueByNestedPath(object, criterion);
    }
    if (typeof object === "object") {
      return object[criterion];
    }
    return object;
  };
  const preparedCriteria = criteria.map((criterion) => {
    if (Array.isArray(criterion) && criterion.length === 1) {
      criterion = criterion[0];
    }
    if (criterion == null || typeof criterion === "function" || Array.isArray(criterion) || isKey(criterion)) {
      return criterion;
    }
    return { key: criterion, path: toPath(criterion) };
  });
  const preparedCollection = collection.map((item) => ({
    original: item,
    criteria: preparedCriteria.map((criterion) => getValueByCriterion(criterion, item))
  }));
  return preparedCollection.slice().sort((a, b) => {
    for (let i = 0; i < preparedCriteria.length; i++) {
      const comparedResult = compareValues3(a.criteria[i], b.criteria[i], orders[i]);
      if (comparedResult !== 0) {
        return comparedResult;
      }
    }
    return 0;
  }).map((item) => item.original);
}

// node_modules/es-toolkit/dist/compat/array/partition.mjs
function partition2(source, predicate = identity) {
  if (!source) {
    return [[], []];
  }
  const collection = isArrayLike(source) ? source : Object.values(source);
  predicate = iteratee(predicate);
  const matched = [];
  const unmatched = [];
  for (let i = 0; i < collection.length; i++) {
    const value = collection[i];
    if (predicate(value)) {
      matched.push(value);
    } else {
      unmatched.push(value);
    }
  }
  return [matched, unmatched];
}

// node_modules/es-toolkit/dist/compat/array/pull.mjs
function pull2(arr, ...valuesToRemove) {
  return pull(arr, valuesToRemove);
}

// node_modules/es-toolkit/dist/compat/array/pullAll.mjs
function pullAll(arr, valuesToRemove = []) {
  return pull(arr, Array.from(valuesToRemove));
}

// node_modules/es-toolkit/dist/compat/array/pullAllBy.mjs
function pullAllBy(arr, valuesToRemove, _getValue) {
  const getValue = iteratee(_getValue);
  const valuesSet = new Set(Array.from(valuesToRemove).map((x) => getValue(x)));
  let resultIndex = 0;
  for (let i = 0; i < arr.length; i++) {
    const value = getValue(arr[i]);
    if (valuesSet.has(value)) {
      continue;
    }
    if (!Object.hasOwn(arr, i)) {
      delete arr[resultIndex++];
      continue;
    }
    arr[resultIndex++] = arr[i];
  }
  arr.length = resultIndex;
  return arr;
}

// node_modules/es-toolkit/dist/compat/_internal/copyArray.mjs
function copyArray(source, array) {
  const length = source.length;
  if (array == null) {
    array = Array(length);
  }
  for (let i = 0; i < length; i++) {
    array[i] = source[i];
  }
  return array;
}

// node_modules/es-toolkit/dist/compat/array/pullAllWith.mjs
function pullAllWith(array, values2, comparator) {
  if (array?.length == null || values2?.length == null) {
    return array;
  }
  if (array === values2) {
    values2 = copyArray(values2);
  }
  let resultLength = 0;
  if (comparator == null) {
    comparator = (a, b) => eq(a, b);
  }
  const valuesArray = Array.isArray(values2) ? values2 : Array.from(values2);
  const hasUndefined = valuesArray.includes(void 0);
  for (let i = 0; i < array.length; i++) {
    if (i in array) {
      const shouldRemove = valuesArray.some((value) => comparator(array[i], value));
      if (!shouldRemove) {
        array[resultLength++] = array[i];
      }
      continue;
    }
    if (!hasUndefined) {
      delete array[resultLength++];
    }
  }
  array.length = resultLength;
  return array;
}

// node_modules/es-toolkit/dist/compat/object/at.mjs
function at2(object, ...paths) {
  if (paths.length === 0) {
    return [];
  }
  const allPaths = [];
  for (let i = 0; i < paths.length; i++) {
    const path = paths[i];
    if (!isArrayLike(path) || isString2(path)) {
      allPaths.push(path);
      continue;
    }
    for (let j = 0; j < path.length; j++) {
      allPaths.push(path[j]);
    }
  }
  const result2 = [];
  for (let i = 0; i < allPaths.length; i++) {
    result2.push(get(object, allPaths[i]));
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/unset.mjs
function unset(obj, path) {
  if (obj == null) {
    return true;
  }
  switch (typeof path) {
    case "symbol":
    case "number":
    case "object": {
      if (Array.isArray(path)) {
        return unsetWithPath(obj, path);
      }
      if (typeof path === "number") {
        path = toKey(path);
      } else if (typeof path === "object") {
        if (Object.is(path?.valueOf(), -0)) {
          path = "-0";
        } else {
          path = String(path);
        }
      }
      if (isUnsafeProperty(path)) {
        return false;
      }
      if (obj?.[path] === void 0) {
        return true;
      }
      try {
        delete obj[path];
        return true;
      } catch {
        return false;
      }
    }
    case "string": {
      if (obj?.[path] === void 0 && isDeepKey(path)) {
        return unsetWithPath(obj, toPath(path));
      }
      if (isUnsafeProperty(path)) {
        return false;
      }
      try {
        delete obj[path];
        return true;
      } catch {
        return false;
      }
    }
  }
}
function unsetWithPath(obj, path) {
  const parent = get(obj, path.slice(0, -1), obj);
  const lastKey = path[path.length - 1];
  if (parent?.[lastKey] === void 0) {
    return true;
  }
  if (isUnsafeProperty(lastKey)) {
    return false;
  }
  try {
    delete parent[lastKey];
    return true;
  } catch {
    return false;
  }
}

// node_modules/es-toolkit/dist/compat/array/pullAt.mjs
function pullAt2(array, ..._indices) {
  const indices = flattenDepth(_indices, 1);
  if (!array) {
    return Array(indices.length);
  }
  const result2 = at2(array, indices);
  const indicesToPull = indices.map((index) => isIndex(index, array.length) ? Number(index) : index).sort((a, b) => b - a);
  for (const index of new Set(indicesToPull)) {
    if (isIndex(index, array.length)) {
      Array.prototype.splice.call(array, index, 1);
      continue;
    }
    if (isKey(index, array)) {
      delete array[toKey(index)];
      continue;
    }
    const path = isArray(index) ? index : toPath(index);
    unset(array, path);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/reduceRight.mjs
function reduceRight(collection, iteratee2 = identity, accumulator) {
  if (!collection) {
    return accumulator;
  }
  let keys2;
  let startIndex;
  if (isArrayLike(collection)) {
    keys2 = range(0, collection.length).reverse();
    if (accumulator == null && collection.length > 0) {
      accumulator = collection[collection.length - 1];
      startIndex = 1;
    } else {
      startIndex = 0;
    }
  } else {
    keys2 = Object.keys(collection).reverse();
    if (accumulator == null) {
      accumulator = collection[keys2[0]];
      startIndex = 1;
    } else {
      startIndex = 0;
    }
  }
  for (let i = startIndex; i < keys2.length; i++) {
    const key = keys2[i];
    const value = collection[key];
    accumulator = iteratee2(accumulator, value, key, collection);
  }
  return accumulator;
}

// node_modules/es-toolkit/dist/compat/function/negate.mjs
function negate2(func) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  return function(...args) {
    return !func.apply(this, args);
  };
}

// node_modules/es-toolkit/dist/compat/array/reject.mjs
function reject(source, predicate = identity) {
  return filter(source, negate2(iteratee(predicate)));
}

// node_modules/es-toolkit/dist/compat/array/remove.mjs
function remove3(arr, shouldRemoveElement = identity) {
  return remove(arr, iteratee(shouldRemoveElement));
}

// node_modules/es-toolkit/dist/compat/array/reverse.mjs
function reverse(array) {
  if (array == null) {
    return array;
  }
  return array.reverse();
}

// node_modules/es-toolkit/dist/compat/array/sample.mjs
function sample2(collection) {
  if (collection == null) {
    return void 0;
  }
  if (isArrayLike(collection)) {
    return sample(toArray(collection));
  }
  return sample(Object.values(collection));
}

// node_modules/es-toolkit/dist/compat/math/clamp.mjs
function clamp3(value, bound1, bound2) {
  if (Number.isNaN(bound1)) {
    bound1 = 0;
  }
  if (Number.isNaN(bound2)) {
    bound2 = 0;
  }
  return clamp(value, bound1, bound2);
}

// node_modules/es-toolkit/dist/compat/predicate/isMap.mjs
function isMap2(value) {
  return isMap(value);
}

// node_modules/es-toolkit/dist/compat/util/toArray.mjs
function toArray2(value) {
  if (value == null) {
    return [];
  }
  if (isArrayLike(value) || isMap2(value)) {
    return Array.from(value);
  }
  if (typeof value === "object") {
    return Object.values(value);
  }
  return [];
}

// node_modules/es-toolkit/dist/compat/array/sampleSize.mjs
function sampleSize2(collection, size2, guard) {
  const arrayCollection = toArray2(collection);
  if (guard ? isIterateeCall(collection, size2, guard) : size2 === void 0) {
    size2 = 1;
  } else {
    size2 = clamp3(toInteger(size2), 0, arrayCollection.length);
  }
  return sampleSize(arrayCollection, size2);
}

// node_modules/es-toolkit/dist/compat/object/values.mjs
function values(object) {
  if (object == null) {
    return [];
  }
  return Object.values(object);
}

// node_modules/es-toolkit/dist/compat/predicate/isNil.mjs
function isNil2(x) {
  return x == null;
}

// node_modules/es-toolkit/dist/compat/array/shuffle.mjs
function shuffle2(collection) {
  if (isNil2(collection)) {
    return [];
  }
  if (isArray(collection)) {
    return shuffle(collection);
  }
  if (isArrayLike(collection)) {
    return shuffle(Array.from(collection));
  }
  if (isObjectLike(collection)) {
    return shuffle(values(collection));
  }
  return [];
}

// node_modules/es-toolkit/dist/compat/array/size.mjs
function size(target) {
  if (isNil(target)) {
    return 0;
  }
  if (target instanceof Map || target instanceof Set) {
    return target.size;
  }
  return Object.keys(target).length;
}

// node_modules/es-toolkit/dist/compat/array/slice.mjs
function slice(array, start3, end) {
  if (!isArrayLike(array)) {
    return [];
  }
  const length = array.length;
  if (end === void 0) {
    end = length;
  } else if (typeof end !== "number" && isIterateeCall(array, start3, end)) {
    start3 = 0;
    end = length;
  }
  start3 = toInteger(start3);
  end = toInteger(end);
  if (start3 < 0) {
    start3 = Math.max(length + start3, 0);
  } else {
    start3 = Math.min(start3, length);
  }
  if (end < 0) {
    end = Math.max(length + end, 0);
  } else {
    end = Math.min(end, length);
  }
  const resultLength = Math.max(end - start3, 0);
  const result2 = new Array(resultLength);
  for (let i = 0; i < resultLength; ++i) {
    result2[i] = array[start3 + i];
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/some.mjs
function some(source, predicate, guard) {
  if (!source) {
    return false;
  }
  if (guard != null) {
    predicate = void 0;
  }
  if (!predicate) {
    predicate = identity;
  }
  const values2 = Array.isArray(source) ? source : Object.values(source);
  switch (typeof predicate) {
    case "function": {
      if (!Array.isArray(source)) {
        const keys2 = Object.keys(source);
        for (let i = 0; i < keys2.length; i++) {
          const key = keys2[i];
          const value = source[key];
          if (predicate(value, key, source)) {
            return true;
          }
        }
        return false;
      }
      for (let i = 0; i < source.length; i++) {
        if (predicate(source[i], i, source)) {
          return true;
        }
      }
      return false;
    }
    case "object": {
      if (Array.isArray(predicate) && predicate.length === 2) {
        const key = predicate[0];
        const value = predicate[1];
        const matchFunc = matchesProperty(key, value);
        if (Array.isArray(source)) {
          for (let i = 0; i < source.length; i++) {
            if (matchFunc(source[i])) {
              return true;
            }
          }
          return false;
        }
        return values2.some(matchFunc);
      } else {
        const matchFunc = matches(predicate);
        if (Array.isArray(source)) {
          for (let i = 0; i < source.length; i++) {
            if (matchFunc(source[i])) {
              return true;
            }
          }
          return false;
        }
        return values2.some(matchFunc);
      }
    }
    case "number":
    case "symbol":
    case "string": {
      const propFunc = property(predicate);
      if (Array.isArray(source)) {
        for (let i = 0; i < source.length; i++) {
          if (propFunc(source[i])) {
            return true;
          }
        }
        return false;
      }
      return values2.some(propFunc);
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/sortBy.mjs
function sortBy2(collection, ...criteria) {
  const length = criteria.length;
  if (length > 1 && isIterateeCall(collection, criteria[0], criteria[1])) {
    criteria = [];
  } else if (length > 2 && isIterateeCall(criteria[0], criteria[1], criteria[2])) {
    criteria = [criteria[0]];
  }
  return orderBy2(collection, flatten(criteria), ["asc"]);
}

// node_modules/es-toolkit/dist/compat/predicate/isNaN.mjs
function isNaN2(value) {
  return Number.isNaN(value);
}

// node_modules/es-toolkit/dist/compat/array/sortedIndexBy.mjs
var MAX_ARRAY_LENGTH = 4294967295;
var MAX_ARRAY_INDEX = MAX_ARRAY_LENGTH - 1;
function sortedIndexBy(array, value, iteratee$1 = iteratee, retHighest) {
  let low = 0;
  let high = array == null ? 0 : array.length;
  if (high === 0 || isNil2(array)) {
    return 0;
  }
  const iterateeFunction = iteratee(iteratee$1);
  const transformedValue = iterateeFunction(value);
  const valIsNaN = isNaN2(transformedValue);
  const valIsNull = isNull(transformedValue);
  const valIsSymbol = isSymbol(transformedValue);
  const valIsUndefined = isUndefined(transformedValue);
  while (low < high) {
    let setLow;
    const mid = Math.floor((low + high) / 2);
    const computed = iterateeFunction(array[mid]);
    const othIsDefined = !isUndefined(computed);
    const othIsNull = isNull(computed);
    const othIsReflexive = !isNaN2(computed);
    const othIsSymbol = isSymbol(computed);
    if (valIsNaN) {
      setLow = retHighest || othIsReflexive;
    } else if (valIsUndefined) {
      setLow = othIsReflexive && (retHighest || othIsDefined);
    } else if (valIsNull) {
      setLow = othIsReflexive && othIsDefined && (retHighest || !othIsNull);
    } else if (valIsSymbol) {
      setLow = othIsReflexive && othIsDefined && !othIsNull && (retHighest || !othIsSymbol);
    } else if (othIsNull || othIsSymbol) {
      setLow = false;
    } else {
      setLow = retHighest ? computed <= transformedValue : computed < transformedValue;
    }
    if (setLow) {
      low = mid + 1;
    } else {
      high = mid;
    }
  }
  return Math.min(high, MAX_ARRAY_INDEX);
}

// node_modules/es-toolkit/dist/compat/predicate/isNumber.mjs
function isNumber(value) {
  return typeof value === "number" || value instanceof Number;
}

// node_modules/es-toolkit/dist/compat/array/sortedIndex.mjs
var MAX_ARRAY_LENGTH2 = 4294967295;
var HALF_MAX_ARRAY_LENGTH = MAX_ARRAY_LENGTH2 >>> 1;
function sortedIndex(array, value) {
  if (isNil(array)) {
    return 0;
  }
  let low = 0, high = isNil(array) ? low : array.length;
  if (isNumber(value) && value === value && high <= HALF_MAX_ARRAY_LENGTH) {
    while (low < high) {
      const mid = low + high >>> 1;
      const compute = array[mid];
      if (!isNull(compute) && !isSymbol2(compute) && compute < value) {
        low = mid + 1;
      } else {
        high = mid;
      }
    }
    return high;
  }
  return sortedIndexBy(array, value, (value2) => value2);
}

// node_modules/es-toolkit/dist/compat/array/sortedIndexOf.mjs
function sortedIndexOf(array, value) {
  if (!array?.length) {
    return -1;
  }
  const index = sortedIndex(array, value);
  if (index < array.length && eq(array[index], value)) {
    return index;
  }
  return -1;
}

// node_modules/es-toolkit/dist/compat/array/sortedLastIndexBy.mjs
function sortedLastIndexBy(array, value, iteratee2) {
  return sortedIndexBy(array, value, iteratee2, true);
}

// node_modules/es-toolkit/dist/compat/array/sortedLastIndex.mjs
var MAX_ARRAY_LENGTH3 = 4294967295;
var HALF_MAX_ARRAY_LENGTH2 = MAX_ARRAY_LENGTH3 >>> 1;
function sortedLastIndex(array, value) {
  if (isNil(array)) {
    return 0;
  }
  let high = array.length;
  if (!isNumber(value) || Number.isNaN(value) || high > HALF_MAX_ARRAY_LENGTH2) {
    return sortedLastIndexBy(array, value, (value2) => value2);
  }
  let low = 0;
  while (low < high) {
    const mid = low + high >>> 1;
    const compute = array[mid];
    if (!isNull(compute) && !isSymbol2(compute) && compute <= value) {
      low = mid + 1;
    } else {
      high = mid;
    }
  }
  return high;
}

// node_modules/es-toolkit/dist/compat/array/sortedLastIndexOf.mjs
function sortedLastIndexOf(array, value) {
  if (!array?.length) {
    return -1;
  }
  const index = sortedLastIndex(array, value) - 1;
  if (index >= 0 && eq(array[index], value)) {
    return index;
  }
  return -1;
}

// node_modules/es-toolkit/dist/compat/array/tail.mjs
function tail2(arr) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return tail(toArray(arr));
}

// node_modules/es-toolkit/dist/compat/array/take.mjs
function take2(arr, count = 1, guard) {
  count = guard ? 1 : toInteger(count);
  if (count < 1 || !isArrayLike(arr)) {
    return [];
  }
  return take(toArray(arr), count);
}

// node_modules/es-toolkit/dist/compat/array/takeRight.mjs
function takeRight2(arr, count = 1, guard) {
  count = guard ? 1 : toInteger(count);
  if (count <= 0 || !isArrayLike(arr)) {
    return [];
  }
  return takeRight(toArray(arr), count);
}

// node_modules/es-toolkit/dist/compat/array/takeRightWhile.mjs
function takeRightWhile2(_array, predicate) {
  if (!isArrayLikeObject(_array)) {
    return [];
  }
  const array = toArray(_array);
  const index = array.findLastIndex(negate(iteratee(predicate ?? identity)));
  return array.slice(index + 1);
}

// node_modules/es-toolkit/dist/compat/function/identity.mjs
function identity2(x) {
  return x;
}

// node_modules/es-toolkit/dist/compat/array/takeWhile.mjs
function takeWhile2(array, predicate) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  const _array = toArray(array);
  const index = _array.findIndex(negate2(iteratee(predicate ?? identity2)));
  return index === -1 ? _array : _array.slice(0, index);
}

// node_modules/es-toolkit/dist/compat/array/union.mjs
function union2(...arrays) {
  const validArrays = arrays.filter(isArrayLikeObject);
  const flattened = flattenDepth(validArrays, 1);
  return uniq(flattened);
}

// node_modules/es-toolkit/dist/compat/array/unionBy.mjs
function unionBy2(...values2) {
  const lastValue = last(values2);
  const flattened = flattenArrayLike(values2);
  if (isArrayLikeObject(lastValue) || lastValue == null) {
    return uniq(flattened);
  }
  return uniqBy(flattened, iteratee(lastValue));
}

// node_modules/es-toolkit/dist/compat/array/unionWith.mjs
function unionWith2(...values2) {
  const lastValue = last(values2);
  const flattened = flattenArrayLike(values2);
  if (isArrayLikeObject(lastValue) || lastValue == null) {
    return uniq(flattened);
  }
  return uniqWith(flattened, lastValue);
}

// node_modules/es-toolkit/dist/compat/array/uniqBy.mjs
function uniqBy2(array, iteratee$1 = identity) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  return uniqBy(Array.from(array), iteratee(iteratee$1));
}

// node_modules/es-toolkit/dist/compat/array/uniqWith.mjs
function uniqWith2(arr, comparator) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return typeof comparator === "function" ? uniqWith(Array.from(arr), comparator) : uniq2(Array.from(arr));
}

// node_modules/es-toolkit/dist/compat/array/unzip.mjs
function unzip2(array) {
  if (!isArrayLikeObject(array) || !array.length) {
    return [];
  }
  array = isArray(array) ? array : Array.from(array);
  array = array.filter((item) => isArrayLikeObject(item));
  return unzip(array);
}

// node_modules/es-toolkit/dist/compat/array/unzipWith.mjs
function unzipWith2(array, iteratee2) {
  if (!isArrayLikeObject(array) || !array.length) {
    return [];
  }
  const unzipped = isArray(array) ? unzip(array) : unzip(Array.from(array, (value) => Array.from(value)));
  if (!iteratee2) {
    return unzipped;
  }
  const result2 = new Array(unzipped.length);
  for (let i = 0; i < unzipped.length; i++) {
    const value = unzipped[i];
    result2[i] = iteratee2(...value);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/without.mjs
function without2(array, ...values2) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  return without(Array.from(array), ...values2);
}

// node_modules/es-toolkit/dist/compat/array/xor.mjs
function xor2(...arrays) {
  const itemCounts = /* @__PURE__ */ new Map();
  for (let i = 0; i < arrays.length; i++) {
    const array = arrays[i];
    if (!isArrayLikeObject(array)) {
      continue;
    }
    const itemSet = new Set(toArray2(array));
    for (const item of itemSet) {
      if (!itemCounts.has(item)) {
        itemCounts.set(item, 1);
      } else {
        itemCounts.set(item, itemCounts.get(item) + 1);
      }
    }
  }
  const result2 = [];
  for (const [item, count] of itemCounts) {
    if (count === 1) {
      result2.push(item);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/xorBy.mjs
function xorBy2(...values2) {
  const lastValue = last2(values2);
  let mapper = identity;
  if (!isArrayLikeObject(lastValue) && lastValue != null) {
    mapper = iteratee(lastValue);
    values2 = values2.slice(0, -1);
  }
  const arrays = values2.filter(isArrayLikeObject);
  const union3 = unionBy2(...arrays, mapper);
  const intersections = windowed(arrays, 2).map(([arr1, arr2]) => intersectionBy2(arr1, arr2, mapper));
  return differenceBy2(union3, unionBy2(...intersections, mapper), mapper);
}

// node_modules/es-toolkit/dist/compat/array/xorWith.mjs
function xorWith2(...values2) {
  const lastValue = last2(values2);
  let comparator = (a, b) => a === b;
  if (typeof lastValue === "function") {
    comparator = lastValue;
    values2 = values2.slice(0, -1);
  }
  const arrays = values2.filter(isArrayLikeObject);
  const union3 = unionWith2(...arrays, comparator);
  const intersections = windowed(arrays, 2).map(([arr1, arr2]) => intersectionWith2(arr1, arr2, comparator));
  return differenceWith2(union3, unionWith2(...intersections, comparator), comparator);
}

// node_modules/es-toolkit/dist/compat/array/zip.mjs
function zip2(...arrays) {
  if (!arrays.length) {
    return [];
  }
  return zip(...arrays.filter((group) => isArrayLikeObject(group)));
}

// node_modules/es-toolkit/dist/compat/_internal/assignValue.mjs
var assignValue = (object, key, value) => {
  const objValue = object[key];
  if (!(Object.hasOwn(object, key) && eq(objValue, value)) || value === void 0 && !(key in object)) {
    object[key] = value;
  }
};

// node_modules/es-toolkit/dist/compat/array/zipObject.mjs
function zipObject2(keys2 = [], values2 = []) {
  const result2 = {};
  for (let i = 0; i < keys2.length; i++) {
    assignValue(result2, keys2[i], values2[i]);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/updateWith.mjs
function updateWith(obj, path, updater, customizer) {
  if (obj == null && !isObject(obj)) {
    return obj;
  }
  const resolvedPath = isKey(path, obj) ? [path] : Array.isArray(path) ? path : typeof path === "string" ? toPath(path) : [path];
  let current = obj;
  for (let i = 0; i < resolvedPath.length && current != null; i++) {
    const key = toKey(resolvedPath[i]);
    if (isUnsafeProperty(key)) {
      continue;
    }
    let newValue;
    if (i === resolvedPath.length - 1) {
      newValue = updater(current[key]);
    } else {
      const objValue = current[key];
      const customizerResult = customizer?.(objValue, key, obj);
      newValue = customizerResult !== void 0 ? customizerResult : isObject(objValue) ? objValue : isIndex(resolvedPath[i + 1]) ? [] : {};
    }
    assignValue(current, key, newValue);
    current = current[key];
  }
  return obj;
}

// node_modules/es-toolkit/dist/compat/object/set.mjs
function set2(obj, path, value) {
  return updateWith(obj, path, () => value, () => void 0);
}

// node_modules/es-toolkit/dist/compat/array/zipObjectDeep.mjs
function zipObjectDeep(keys2, values2) {
  const result2 = {};
  if (!isArrayLike(keys2)) {
    return result2;
  }
  if (!isArrayLike(values2)) {
    values2 = [];
  }
  const zipped = zip(Array.from(keys2), Array.from(values2));
  for (let i = 0; i < zipped.length; i++) {
    const [key, value] = zipped[i];
    if (key != null) {
      set2(result2, key, value);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/zipWith.mjs
function zipWith2(...combine) {
  let iteratee2 = combine.pop();
  if (!isFunction(iteratee2)) {
    combine.push(iteratee2);
    iteratee2 = void 0;
  }
  if (!combine?.length) {
    return [];
  }
  const result2 = unzip2(combine);
  if (iteratee2 == null) {
    return result2;
  }
  return result2.map((group) => iteratee2(...group));
}

// node_modules/es-toolkit/dist/compat/function/after.mjs
function after2(n, func) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  n = toInteger(n);
  return function(...args) {
    if (--n < 1) {
      return func.apply(this, args);
    }
  };
}

// node_modules/es-toolkit/dist/compat/function/ary.mjs
function ary2(func, n = func.length, guard) {
  if (guard) {
    n = func.length;
  }
  if (Number.isNaN(n) || n < 0) {
    n = 0;
  }
  return ary(func, n);
}

// node_modules/es-toolkit/dist/compat/function/attempt.mjs
function attempt2(func, ...args) {
  try {
    return func(...args);
  } catch (e) {
    return e instanceof Error ? e : new Error(e);
  }
}

// node_modules/es-toolkit/dist/compat/function/before.mjs
function before2(n, func) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  let result2;
  n = toInteger(n);
  return function(...args) {
    if (--n > 0) {
      result2 = func.apply(this, args);
    }
    if (n <= 1 && func) {
      func = void 0;
    }
    return result2;
  };
}

// node_modules/es-toolkit/dist/compat/function/bind.mjs
function bind(func, thisObj, ...partialArgs) {
  const bound = function(...providedArgs) {
    const args = [];
    let startIndex = 0;
    for (let i = 0; i < partialArgs.length; i++) {
      const arg = partialArgs[i];
      if (arg === bind.placeholder) {
        args.push(providedArgs[startIndex++]);
      } else {
        args.push(arg);
      }
    }
    for (let i = startIndex; i < providedArgs.length; i++) {
      args.push(providedArgs[i]);
    }
    if (this instanceof bound) {
      return new func(...args);
    }
    return func.apply(thisObj, args);
  };
  return bound;
}
var bindPlaceholder = Symbol("bind.placeholder");
bind.placeholder = bindPlaceholder;

// node_modules/es-toolkit/dist/compat/function/bindKey.mjs
function bindKey(object, key, ...partialArgs) {
  const bound = function(...providedArgs) {
    const args = [];
    let startIndex = 0;
    for (let i = 0; i < partialArgs.length; i++) {
      const arg = partialArgs[i];
      if (arg === bindKey.placeholder) {
        args.push(providedArgs[startIndex++]);
      } else {
        args.push(arg);
      }
    }
    for (let i = startIndex; i < providedArgs.length; i++) {
      args.push(providedArgs[i]);
    }
    if (this instanceof bound) {
      return new object[key](...args);
    }
    return object[key].apply(object, args);
  };
  return bound;
}
var bindKeyPlaceholder = Symbol("bindKey.placeholder");
bindKey.placeholder = bindKeyPlaceholder;

// node_modules/es-toolkit/dist/compat/function/curry.mjs
function curry2(func, arity = func.length, guard) {
  arity = guard ? func.length : arity;
  arity = Number.parseInt(arity, 10);
  if (Number.isNaN(arity) || arity < 1) {
    arity = 0;
  }
  const wrapper = function(...partialArgs) {
    const holders = partialArgs.filter((item) => item === curry2.placeholder);
    const length = partialArgs.length - holders.length;
    if (length < arity) {
      return makeCurry(func, arity - length, partialArgs);
    }
    if (this instanceof wrapper) {
      return new func(...partialArgs);
    }
    return func.apply(this, partialArgs);
  };
  wrapper.placeholder = curryPlaceholder;
  return wrapper;
}
function makeCurry(func, arity, partialArgs) {
  function wrapper(...providedArgs) {
    const holders = providedArgs.filter((item) => item === curry2.placeholder);
    const length = providedArgs.length - holders.length;
    providedArgs = composeArgs(providedArgs, partialArgs);
    if (length < arity) {
      return makeCurry(func, arity - length, providedArgs);
    }
    if (this instanceof wrapper) {
      return new func(...providedArgs);
    }
    return func.apply(this, providedArgs);
  }
  wrapper.placeholder = curryPlaceholder;
  return wrapper;
}
function composeArgs(providedArgs, partialArgs) {
  const args = [];
  let startIndex = 0;
  for (let i = 0; i < partialArgs.length; i++) {
    const arg = partialArgs[i];
    if (arg === curry2.placeholder && startIndex < providedArgs.length) {
      args.push(providedArgs[startIndex++]);
    } else {
      args.push(arg);
    }
  }
  for (let i = startIndex; i < providedArgs.length; i++) {
    args.push(providedArgs[i]);
  }
  return args;
}
var curryPlaceholder = Symbol("curry.placeholder");
curry2.placeholder = curryPlaceholder;

// node_modules/es-toolkit/dist/compat/function/curryRight.mjs
function curryRight2(func, arity = func.length, guard) {
  arity = guard ? func.length : arity;
  arity = Number.parseInt(arity, 10);
  if (Number.isNaN(arity) || arity < 1) {
    arity = 0;
  }
  const wrapper = function(...partialArgs) {
    const holders = partialArgs.filter((item) => item === curryRight2.placeholder);
    const length = partialArgs.length - holders.length;
    if (length < arity) {
      return makeCurryRight(func, arity - length, partialArgs);
    }
    if (this instanceof wrapper) {
      return new func(...partialArgs);
    }
    return func.apply(this, partialArgs);
  };
  wrapper.placeholder = curryRightPlaceholder;
  return wrapper;
}
function makeCurryRight(func, arity, partialArgs) {
  function wrapper(...providedArgs) {
    const holders = providedArgs.filter((item) => item === curryRight2.placeholder);
    const length = providedArgs.length - holders.length;
    providedArgs = composeArgs2(providedArgs, partialArgs);
    if (length < arity) {
      return makeCurryRight(func, arity - length, providedArgs);
    }
    if (this instanceof wrapper) {
      return new func(...providedArgs);
    }
    return func.apply(this, providedArgs);
  }
  wrapper.placeholder = curryRightPlaceholder;
  return wrapper;
}
function composeArgs2(providedArgs, partialArgs) {
  const placeholderLength = partialArgs.filter((arg) => arg === curryRight2.placeholder).length;
  const rangeLength = Math.max(providedArgs.length - placeholderLength, 0);
  const args = [];
  let providedIndex = 0;
  for (let i = 0; i < rangeLength; i++) {
    args.push(providedArgs[providedIndex++]);
  }
  for (let i = 0; i < partialArgs.length; i++) {
    const arg = partialArgs[i];
    if (arg === curryRight2.placeholder) {
      if (providedIndex < providedArgs.length) {
        args.push(providedArgs[providedIndex++]);
      } else {
        args.push(arg);
      }
    } else {
      args.push(arg);
    }
  }
  return args;
}
var curryRightPlaceholder = Symbol("curryRight.placeholder");
curryRight2.placeholder = curryRightPlaceholder;

// node_modules/es-toolkit/dist/compat/function/debounce.mjs
function debounce3(func, debounceMs = 0, options = {}) {
  if (typeof options !== "object") {
    options = {};
  }
  const { leading = false, trailing = true, maxWait } = options;
  const edges = Array(2);
  if (leading) {
    edges[0] = "leading";
  }
  if (trailing) {
    edges[1] = "trailing";
  }
  let result2 = void 0;
  let pendingAt = null;
  const _debounced = debounce(function(...args) {
    result2 = func.apply(this, args);
    pendingAt = null;
  }, debounceMs, { edges });
  const debounced = function(...args) {
    if (maxWait != null) {
      if (pendingAt === null) {
        pendingAt = Date.now();
      }
      if (Date.now() - pendingAt >= maxWait) {
        result2 = func.apply(this, args);
        pendingAt = Date.now();
        _debounced.cancel();
        _debounced.schedule();
        return result2;
      }
    }
    _debounced.apply(this, args);
    return result2;
  };
  const flush = () => {
    _debounced.flush();
    return result2;
  };
  debounced.cancel = _debounced.cancel;
  debounced.flush = flush;
  return debounced;
}

// node_modules/es-toolkit/dist/compat/function/defer.mjs
function defer(func, ...args) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  return setTimeout(func, 1, ...args);
}

// node_modules/es-toolkit/dist/compat/function/delay.mjs
function delay2(func, wait, ...args) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  return setTimeout(func, toNumber(wait) || 0, ...args);
}

// node_modules/es-toolkit/dist/compat/function/flip.mjs
function flip(func) {
  return function(...args) {
    return func.apply(this, args.reverse());
  };
}

// node_modules/es-toolkit/dist/compat/function/flow.mjs
function flow2(...funcs) {
  const flattenFuncs = flatten(funcs, 1);
  if (flattenFuncs.some((func) => typeof func !== "function")) {
    throw new TypeError("Expected a function");
  }
  return flow(...flattenFuncs);
}

// node_modules/es-toolkit/dist/compat/function/flowRight.mjs
function flowRight2(...funcs) {
  const flattenFuncs = flatten(funcs, 1);
  if (flattenFuncs.some((func) => typeof func !== "function")) {
    throw new TypeError("Expected a function");
  }
  return flowRight(...flattenFuncs);
}

// node_modules/es-toolkit/dist/compat/function/memoize.mjs
function memoize2(func, resolver) {
  if (typeof func !== "function" || resolver != null && typeof resolver !== "function") {
    throw new TypeError("Expected a function");
  }
  const memoized = function(...args) {
    const key = resolver ? resolver.apply(this, args) : args[0];
    const cache = memoized.cache;
    if (cache.has(key)) {
      return cache.get(key);
    }
    const result2 = func.apply(this, args);
    memoized.cache = cache.set(key, result2) || cache;
    return result2;
  };
  const CacheConstructor = memoize2.Cache || Map;
  memoized.cache = new CacheConstructor();
  return memoized;
}
memoize2.Cache = Map;

// node_modules/es-toolkit/dist/compat/function/nthArg.mjs
function nthArg(n = 0) {
  return function(...args) {
    return args.at(toInteger(n));
  };
}

// node_modules/es-toolkit/dist/compat/function/once.mjs
function once2(func) {
  return once(func);
}

// node_modules/es-toolkit/dist/compat/function/overArgs.mjs
function overArgs(func, ..._transforms) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  const transforms = _transforms.flat();
  return function(...args) {
    const length = Math.min(args.length, transforms.length);
    const transformedArgs = [...args];
    for (let i = 0; i < length; i++) {
      const transform2 = iteratee(transforms[i] ?? identity);
      transformedArgs[i] = transform2.call(this, args[i]);
    }
    return func.apply(this, transformedArgs);
  };
}

// node_modules/es-toolkit/dist/compat/function/partial.mjs
function partial2(func, ...partialArgs) {
  return partialImpl(func, partial2.placeholder, ...partialArgs);
}
partial2.placeholder = Symbol("compat.partial.placeholder");

// node_modules/es-toolkit/dist/compat/function/partialRight.mjs
function partialRight2(func, ...partialArgs) {
  return partialRightImpl(func, partialRight2.placeholder, ...partialArgs);
}
partialRight2.placeholder = Symbol("compat.partialRight.placeholder");

// node_modules/es-toolkit/dist/compat/function/rearg.mjs
function rearg(func, ...indices) {
  const flattenIndices = flatten2(indices);
  return function(...args) {
    const reorderedArgs = flattenIndices.map((i) => args[i]).slice(0, args.length);
    for (let i = reorderedArgs.length; i < args.length; i++) {
      reorderedArgs.push(args[i]);
    }
    return func.apply(this, reorderedArgs);
  };
}

// node_modules/es-toolkit/dist/compat/function/rest.mjs
function rest2(func, start3 = func.length - 1) {
  start3 = Number.parseInt(start3, 10);
  if (Number.isNaN(start3) || start3 < 0) {
    start3 = func.length - 1;
  }
  return rest(func, start3);
}

// node_modules/es-toolkit/dist/compat/function/spread.mjs
function spread2(func, argsIndex = 0) {
  argsIndex = Number.parseInt(argsIndex, 10);
  if (Number.isNaN(argsIndex) || argsIndex < 0) {
    argsIndex = 0;
  }
  return function(...args) {
    const array = args[argsIndex];
    const params = args.slice(0, argsIndex);
    if (array) {
      params.push(...array);
    }
    return func.apply(this, params);
  };
}

// node_modules/es-toolkit/dist/compat/function/throttle.mjs
function throttle2(func, throttleMs = 0, options = {}) {
  const { leading = true, trailing = true } = options;
  return debounce3(func, throttleMs, {
    leading,
    maxWait: throttleMs,
    trailing
  });
}

// node_modules/es-toolkit/dist/compat/function/unary.mjs
function unary2(func) {
  return ary2(func, 1);
}

// node_modules/es-toolkit/dist/compat/function/wrap.mjs
function wrap(value, wrapper) {
  return function(...args) {
    const wrapFn = isFunction(wrapper) ? wrapper : identity;
    return wrapFn.apply(this, [value, ...args]);
  };
}

// node_modules/es-toolkit/dist/compat/util/toString.mjs
function toString(value) {
  if (value == null) {
    return "";
  }
  if (typeof value === "string") {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(toString).join(",");
  }
  const result2 = String(value);
  if (result2 === "0" && Object.is(Number(value), -0)) {
    return "-0";
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/math/add.mjs
function add(value, other) {
  if (value === void 0 && other === void 0) {
    return 0;
  }
  if (value === void 0 || other === void 0) {
    return value ?? other;
  }
  if (typeof value === "string" || typeof other === "string") {
    value = toString(value);
    other = toString(other);
  } else {
    value = toNumber(value);
    other = toNumber(other);
  }
  return value + other;
}

// node_modules/es-toolkit/dist/compat/_internal/decimalAdjust.mjs
function decimalAdjust(type, number, precision = 0) {
  number = Number(number);
  if (Object.is(number, -0)) {
    number = "-0";
  }
  precision = Math.min(Number.parseInt(precision, 10), 292);
  if (precision) {
    const [magnitude, exponent = 0] = number.toString().split("e");
    let adjustedValue = Math[type](Number(`${magnitude}e${Number(exponent) + precision}`));
    if (Object.is(adjustedValue, -0)) {
      adjustedValue = "-0";
    }
    const [newMagnitude, newExponent = 0] = adjustedValue.toString().split("e");
    return Number(`${newMagnitude}e${Number(newExponent) - precision}`);
  }
  return Math[type](Number(number));
}

// node_modules/es-toolkit/dist/compat/math/ceil.mjs
function ceil(number, precision = 0) {
  return decimalAdjust("ceil", number, precision);
}

// node_modules/es-toolkit/dist/compat/math/divide.mjs
function divide(value, other) {
  if (value === void 0 && other === void 0) {
    return 1;
  }
  if (value === void 0 || other === void 0) {
    return value ?? other;
  }
  if (typeof value === "string" || typeof other === "string") {
    value = toString(value);
    other = toString(other);
  } else {
    value = toNumber(value);
    other = toNumber(other);
  }
  return value / other;
}

// node_modules/es-toolkit/dist/compat/math/floor.mjs
function floor(number, precision = 0) {
  return decimalAdjust("floor", number, precision);
}

// node_modules/es-toolkit/dist/compat/math/inRange.mjs
function inRange2(value, minimum, maximum) {
  if (!minimum) {
    minimum = 0;
  }
  if (maximum != null && !maximum) {
    maximum = 0;
  }
  if (minimum != null && typeof minimum !== "number") {
    minimum = Number(minimum);
  }
  if (maximum == null && minimum === 0) {
    return false;
  }
  if (maximum != null && typeof maximum !== "number") {
    maximum = Number(maximum);
  }
  if (maximum != null && minimum > maximum) {
    [minimum, maximum] = [maximum, minimum];
  }
  if (minimum === maximum) {
    return false;
  }
  return inRange(value, minimum, maximum);
}

// node_modules/es-toolkit/dist/compat/math/max.mjs
function max(items) {
  if (!items || items.length === 0) {
    return void 0;
  }
  let maxResult = void 0;
  for (let i = 0; i < items.length; i++) {
    const current = items[i];
    if (current == null || Number.isNaN(current) || typeof current === "symbol") {
      continue;
    }
    if (maxResult === void 0 || current > maxResult) {
      maxResult = current;
    }
  }
  return maxResult;
}

// node_modules/es-toolkit/dist/compat/math/maxBy.mjs
function maxBy2(items, iteratee$1) {
  if (items == null) {
    return void 0;
  }
  return maxBy(Array.from(items), iteratee(iteratee$1 ?? identity));
}

// node_modules/es-toolkit/dist/compat/math/sumBy.mjs
function sumBy2(array, iteratee$1) {
  if (!array || !array.length) {
    return 0;
  }
  if (iteratee$1 != null) {
    iteratee$1 = iteratee(iteratee$1);
  }
  let result2 = void 0;
  for (let i = 0; i < array.length; i++) {
    const current = iteratee$1 ? iteratee$1(array[i]) : array[i];
    if (current !== void 0) {
      if (result2 === void 0) {
        result2 = current;
      } else {
        result2 += current;
      }
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/math/sum.mjs
function sum2(array) {
  return sumBy2(array);
}

// node_modules/es-toolkit/dist/compat/math/mean.mjs
function mean2(nums) {
  const length = nums ? nums.length : 0;
  return length === 0 ? NaN : sum2(nums) / length;
}

// node_modules/es-toolkit/dist/compat/math/meanBy.mjs
function meanBy2(items, iteratee$1) {
  if (items == null) {
    return NaN;
  }
  return meanBy(Array.from(items), iteratee(iteratee$1 ?? identity));
}

// node_modules/es-toolkit/dist/compat/math/min.mjs
function min(items) {
  if (!items || items.length === 0) {
    return void 0;
  }
  let minResult = void 0;
  for (let i = 0; i < items.length; i++) {
    const current = items[i];
    if (current == null || Number.isNaN(current) || typeof current === "symbol") {
      continue;
    }
    if (minResult === void 0 || current < minResult) {
      minResult = current;
    }
  }
  return minResult;
}

// node_modules/es-toolkit/dist/compat/math/minBy.mjs
function minBy2(items, iteratee$1) {
  if (items == null) {
    return void 0;
  }
  return minBy(Array.from(items), iteratee(iteratee$1 ?? identity));
}

// node_modules/es-toolkit/dist/compat/math/multiply.mjs
function multiply(value, other) {
  if (value === void 0 && other === void 0) {
    return 1;
  }
  if (value === void 0 || other === void 0) {
    return value ?? other;
  }
  if (typeof value === "string" || typeof other === "string") {
    value = toString(value);
    other = toString(other);
  } else {
    value = toNumber(value);
    other = toNumber(other);
  }
  return value * other;
}

// node_modules/es-toolkit/dist/compat/math/parseInt.mjs
function parseInt2(string, radix = 0, guard) {
  if (guard) {
    radix = 0;
  }
  return Number.parseInt(string, radix);
}

// node_modules/es-toolkit/dist/compat/math/random.mjs
function random2(...args) {
  let minimum = 0;
  let maximum = 1;
  let floating = false;
  switch (args.length) {
    case 1: {
      if (typeof args[0] === "boolean") {
        floating = args[0];
      } else {
        maximum = args[0];
      }
      break;
    }
    case 2: {
      if (typeof args[1] === "boolean") {
        maximum = args[0];
        floating = args[1];
      } else {
        minimum = args[0];
        maximum = args[1];
      }
    }
    case 3: {
      if (typeof args[2] === "object" && args[2] != null && args[2][args[1]] === args[0]) {
        minimum = 0;
        maximum = args[0];
        floating = false;
      } else {
        minimum = args[0];
        maximum = args[1];
        floating = args[2];
      }
    }
  }
  if (typeof minimum !== "number") {
    minimum = Number(minimum);
  }
  if (typeof maximum !== "number") {
    minimum = Number(maximum);
  }
  if (!minimum) {
    minimum = 0;
  }
  if (!maximum) {
    maximum = 0;
  }
  if (minimum > maximum) {
    [minimum, maximum] = [maximum, minimum];
  }
  minimum = clamp3(minimum, -Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER);
  maximum = clamp3(maximum, -Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER);
  if (minimum === maximum) {
    return minimum;
  }
  if (floating) {
    return random(minimum, maximum + 1);
  } else {
    return randomInt(minimum, maximum + 1);
  }
}

// node_modules/es-toolkit/dist/compat/math/range.mjs
function range2(start3, end, step) {
  if (step && typeof step !== "number" && isIterateeCall(start3, end, step)) {
    end = step = void 0;
  }
  start3 = toFinite(start3);
  if (end === void 0) {
    end = start3;
    start3 = 0;
  } else {
    end = toFinite(end);
  }
  step = step === void 0 ? start3 < end ? 1 : -1 : toFinite(step);
  const length = Math.max(Math.ceil((end - start3) / (step || 1)), 0);
  const result2 = new Array(length);
  for (let index = 0; index < length; index++) {
    result2[index] = start3;
    start3 += step;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/math/rangeRight.mjs
function rangeRight2(start3, end, step) {
  if (step && typeof step !== "number" && isIterateeCall(start3, end, step)) {
    end = step = void 0;
  }
  start3 = toFinite(start3);
  if (end === void 0) {
    end = start3;
    start3 = 0;
  } else {
    end = toFinite(end);
  }
  step = step === void 0 ? start3 < end ? 1 : -1 : toFinite(step);
  const length = Math.max(Math.ceil((end - start3) / (step || 1)), 0);
  const result2 = new Array(length);
  for (let index = length - 1; index >= 0; index--) {
    result2[index] = start3;
    start3 += step;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/math/round.mjs
function round2(number, precision = 0) {
  return decimalAdjust("round", number, precision);
}

// node_modules/es-toolkit/dist/compat/math/subtract.mjs
function subtract(value, other) {
  if (value === void 0 && other === void 0) {
    return 0;
  }
  if (value === void 0 || other === void 0) {
    return value ?? other;
  }
  if (typeof value === "string" || typeof other === "string") {
    value = toString(value);
    other = toString(other);
  } else {
    value = toNumber(value);
    other = toNumber(other);
  }
  return value - other;
}

// node_modules/es-toolkit/dist/compat/function/noop.mjs
function noop2(..._) {
}

// node_modules/es-toolkit/dist/compat/_internal/isPrototype.mjs
function isPrototype(value) {
  const constructor = value?.constructor;
  const prototype = typeof constructor === "function" ? constructor.prototype : Object.prototype;
  return value === prototype;
}

// node_modules/es-toolkit/dist/compat/predicate/isTypedArray.mjs
function isTypedArray2(x) {
  return isTypedArray(x);
}

// node_modules/es-toolkit/dist/compat/util/times.mjs
function times(n, getValue) {
  n = toInteger(n);
  if (n < 1 || !Number.isSafeInteger(n)) {
    return [];
  }
  const result2 = new Array(n);
  for (let i = 0; i < n; i++) {
    result2[i] = typeof getValue === "function" ? getValue(i) : i;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/keys.mjs
function keys(object) {
  if (isArrayLike(object)) {
    return arrayLikeKeys(object);
  }
  const result2 = Object.keys(Object(object));
  if (!isPrototype(object)) {
    return result2;
  }
  return result2.filter((key) => key !== "constructor");
}
function arrayLikeKeys(object) {
  const indices = times(object.length, (index) => `${index}`);
  const filteredKeys = new Set(indices);
  if (isBuffer(object)) {
    filteredKeys.add("offset");
    filteredKeys.add("parent");
  }
  if (isTypedArray2(object)) {
    filteredKeys.add("buffer");
    filteredKeys.add("byteLength");
    filteredKeys.add("byteOffset");
  }
  return [...indices, ...Object.keys(object).filter((key) => !filteredKeys.has(key))];
}

// node_modules/es-toolkit/dist/compat/object/assign.mjs
function assign(object, ...sources) {
  for (let i = 0; i < sources.length; i++) {
    assignImpl(object, sources[i]);
  }
  return object;
}
function assignImpl(object, source) {
  const keys$1 = keys(source);
  for (let i = 0; i < keys$1.length; i++) {
    const key = keys$1[i];
    if (!(key in object) || !eq(object[key], source[key])) {
      object[key] = source[key];
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/keysIn.mjs
function keysIn(object) {
  if (object == null) {
    return [];
  }
  switch (typeof object) {
    case "object":
    case "function": {
      if (isArrayLike(object)) {
        return arrayLikeKeysIn(object);
      }
      if (isPrototype(object)) {
        return prototypeKeysIn(object);
      }
      return keysInImpl(object);
    }
    default: {
      return keysInImpl(Object(object));
    }
  }
}
function keysInImpl(object) {
  const result2 = [];
  for (const key in object) {
    result2.push(key);
  }
  return result2;
}
function prototypeKeysIn(object) {
  const keys2 = keysInImpl(object);
  return keys2.filter((key) => key !== "constructor");
}
function arrayLikeKeysIn(object) {
  const indices = times(object.length, (index) => `${index}`);
  const filteredKeys = new Set(indices);
  if (isBuffer(object)) {
    filteredKeys.add("offset");
    filteredKeys.add("parent");
  }
  if (isTypedArray2(object)) {
    filteredKeys.add("buffer");
    filteredKeys.add("byteLength");
    filteredKeys.add("byteOffset");
  }
  return [...indices, ...keysInImpl(object).filter((key) => !filteredKeys.has(key))];
}

// node_modules/es-toolkit/dist/compat/object/assignIn.mjs
function assignIn(object, ...sources) {
  for (let i = 0; i < sources.length; i++) {
    assignInImpl(object, sources[i]);
  }
  return object;
}
function assignInImpl(object, source) {
  const keys2 = keysIn(source);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    if (!(key in object) || !eq(object[key], source[key])) {
      object[key] = source[key];
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/assignInWith.mjs
function assignInWith(object, ...sources) {
  let getValueToAssign = sources[sources.length - 1];
  if (typeof getValueToAssign === "function") {
    sources.pop();
  } else {
    getValueToAssign = void 0;
  }
  for (let i = 0; i < sources.length; i++) {
    assignInWithImpl(object, sources[i], getValueToAssign);
  }
  return object;
}
function assignInWithImpl(object, source, getValueToAssign) {
  const keys2 = keysIn(source);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const objValue = object[key];
    const srcValue = source[key];
    const newValue = getValueToAssign?.(objValue, srcValue, key, object, source) ?? srcValue;
    if (!(key in object) || !eq(objValue, newValue)) {
      object[key] = newValue;
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/assignWith.mjs
function assignWith(object, ...sources) {
  let getValueToAssign = sources[sources.length - 1];
  if (typeof getValueToAssign === "function") {
    sources.pop();
  } else {
    getValueToAssign = void 0;
  }
  for (let i = 0; i < sources.length; i++) {
    assignWithImpl(object, sources[i], getValueToAssign);
  }
  return object;
}
function assignWithImpl(object, source, getValueToAssign) {
  const keys$1 = keys(source);
  for (let i = 0; i < keys$1.length; i++) {
    const key = keys$1[i];
    const objValue = object[key];
    const srcValue = source[key];
    const newValue = getValueToAssign?.(objValue, srcValue, key, object, source) ?? srcValue;
    if (!(key in object) || !eq(objValue, newValue)) {
      object[key] = newValue;
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/clone.mjs
function clone2(obj) {
  if (isPrimitive(obj)) {
    return obj;
  }
  const tag = getTag(obj);
  if (!isCloneableObject2(obj)) {
    return {};
  }
  if (isArray(obj)) {
    const result3 = Array.from(obj);
    if (obj.length > 0 && typeof obj[0] === "string" && Object.hasOwn(obj, "index")) {
      result3.index = obj.index;
      result3.input = obj.input;
    }
    return result3;
  }
  if (isTypedArray2(obj)) {
    const typedArray = obj;
    const Ctor = typedArray.constructor;
    return new Ctor(typedArray.buffer, typedArray.byteOffset, typedArray.length);
  }
  if (tag === arrayBufferTag) {
    return new ArrayBuffer(obj.byteLength);
  }
  if (tag === dataViewTag) {
    const dataView = obj;
    const buffer = dataView.buffer;
    const byteOffset = dataView.byteOffset;
    const byteLength = dataView.byteLength;
    const clonedBuffer = new ArrayBuffer(byteLength);
    const srcView = new Uint8Array(buffer, byteOffset, byteLength);
    const destView = new Uint8Array(clonedBuffer);
    destView.set(srcView);
    return new DataView(clonedBuffer);
  }
  if (tag === booleanTag || tag === numberTag || tag === stringTag) {
    const Ctor = obj.constructor;
    const clone3 = new Ctor(obj.valueOf());
    if (tag === stringTag) {
      cloneStringObjectProperties(clone3, obj);
    } else {
      copyOwnProperties(clone3, obj);
    }
    return clone3;
  }
  if (tag === dateTag) {
    return new Date(Number(obj));
  }
  if (tag === regexpTag) {
    const regExp = obj;
    const clone3 = new RegExp(regExp.source, regExp.flags);
    clone3.lastIndex = regExp.lastIndex;
    return clone3;
  }
  if (tag === symbolTag) {
    return Object(Symbol.prototype.valueOf.call(obj));
  }
  if (tag === mapTag) {
    const map2 = obj;
    const result3 = /* @__PURE__ */ new Map();
    map2.forEach((obj2, key) => {
      result3.set(key, obj2);
    });
    return result3;
  }
  if (tag === setTag) {
    const set3 = obj;
    const result3 = /* @__PURE__ */ new Set();
    set3.forEach((obj2) => {
      result3.add(obj2);
    });
    return result3;
  }
  if (tag === argumentsTag) {
    const args = obj;
    const result3 = {};
    copyOwnProperties(result3, args);
    result3.length = args.length;
    result3[Symbol.iterator] = args[Symbol.iterator];
    return result3;
  }
  const result2 = {};
  copyPrototype(result2, obj);
  copyOwnProperties(result2, obj);
  copySymbolProperties(result2, obj);
  return result2;
}
function isCloneableObject2(object) {
  switch (getTag(object)) {
    case argumentsTag:
    case arrayTag:
    case arrayBufferTag:
    case dataViewTag:
    case booleanTag:
    case dateTag:
    case float32ArrayTag:
    case float64ArrayTag:
    case int8ArrayTag:
    case int16ArrayTag:
    case int32ArrayTag:
    case mapTag:
    case numberTag:
    case objectTag:
    case regexpTag:
    case setTag:
    case stringTag:
    case symbolTag:
    case uint8ArrayTag:
    case uint8ClampedArrayTag:
    case uint16ArrayTag:
    case uint32ArrayTag: {
      return true;
    }
    default: {
      return false;
    }
  }
}
function copyOwnProperties(target, source) {
  for (const key in source) {
    if (Object.hasOwn(source, key)) {
      target[key] = source[key];
    }
  }
}
function copySymbolProperties(target, source) {
  const symbols = Object.getOwnPropertySymbols(source);
  for (let i = 0; i < symbols.length; i++) {
    const symbol = symbols[i];
    if (Object.prototype.propertyIsEnumerable.call(source, symbol)) {
      target[symbol] = source[symbol];
    }
  }
}
function cloneStringObjectProperties(target, source) {
  const stringLength = source.valueOf().length;
  for (const key in source) {
    if (Object.hasOwn(source, key) && (Number.isNaN(Number(key)) || Number(key) >= stringLength)) {
      target[key] = source[key];
    }
  }
}
function copyPrototype(target, source) {
  const proto = Object.getPrototypeOf(source);
  if (proto !== null) {
    const Ctor = source.constructor;
    if (typeof Ctor === "function") {
      Object.setPrototypeOf(target, proto);
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/cloneWith.mjs
function cloneWith(value, customizer) {
  if (!customizer) {
    return clone2(value);
  }
  const result2 = customizer(value);
  if (result2 !== void 0) {
    return result2;
  }
  return clone2(value);
}

// node_modules/es-toolkit/dist/compat/object/create.mjs
function create(prototype, properties) {
  const proto = isObject(prototype) ? Object.create(prototype) : {};
  if (properties != null) {
    const propsKeys = keys(properties);
    for (let i = 0; i < propsKeys.length; i++) {
      const key = propsKeys[i];
      const propsValue = properties[key];
      assignValue(proto, key, propsValue);
    }
  }
  return proto;
}

// node_modules/es-toolkit/dist/compat/object/defaults.mjs
function defaults(object, ...sources) {
  object = Object(object);
  const objectProto = Object.prototype;
  let length = sources.length;
  const guard = length > 2 ? sources[2] : void 0;
  if (guard && isIterateeCall(sources[0], sources[1], guard)) {
    length = 1;
  }
  for (let i = 0; i < length; i++) {
    const source = sources[i];
    const keys2 = Object.keys(source);
    for (let j = 0; j < keys2.length; j++) {
      const key = keys2[j];
      const value = object[key];
      if (value === void 0 || !Object.hasOwn(object, key) && eq(value, objectProto[key])) {
        object[key] = source[key];
      }
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/defaultsDeep.mjs
function defaultsDeep(target, ...sources) {
  target = Object(target);
  for (let i = 0; i < sources.length; i++) {
    const source = sources[i];
    if (source != null) {
      const stack = /* @__PURE__ */ new WeakMap();
      defaultsDeepRecursive(target, source, stack);
    }
  }
  return target;
}
function defaultsDeepRecursive(target, source, stack) {
  for (const key in source) {
    const sourceValue = source[key];
    const targetValue = target[key];
    const targetHasKey = Object.hasOwn(target, key);
    if (!targetHasKey || targetValue === void 0) {
      if (stack.has(sourceValue)) {
        target[key] = stack.get(sourceValue);
      } else if (isPlainObject2(sourceValue)) {
        const newObj = {};
        stack.set(sourceValue, newObj);
        target[key] = newObj;
        defaultsDeepRecursive(newObj, sourceValue, stack);
      } else {
        target[key] = sourceValue;
      }
    } else if (isPlainObject2(targetValue) && isPlainObject2(sourceValue)) {
      const inStack = stack.has(sourceValue);
      if (!inStack || inStack && stack.get(sourceValue) !== targetValue) {
        stack.set(sourceValue, targetValue);
        defaultsDeepRecursive(targetValue, sourceValue, stack);
      }
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/findKey.mjs
function findKey2(obj, predicate) {
  if (!isObject(obj)) {
    return void 0;
  }
  const iteratee$1 = iteratee(predicate ?? identity2);
  return findKey(obj, iteratee$1);
}

// node_modules/es-toolkit/dist/compat/object/findLastKey.mjs
function findLastKey(obj, predicate) {
  if (!isObject(obj)) {
    return void 0;
  }
  const iteratee$1 = iteratee(predicate ?? identity2);
  const keys2 = Object.keys(obj);
  return keys2.findLast((key) => iteratee$1(obj[key], key, obj));
}

// node_modules/es-toolkit/dist/compat/object/forIn.mjs
function forIn(object, iteratee2 = identity) {
  if (object == null) {
    return object;
  }
  for (const key in object) {
    const result2 = iteratee2(object[key], key, object);
    if (result2 === false) {
      break;
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/forInRight.mjs
function forInRight(object, iteratee2 = identity) {
  if (object == null) {
    return object;
  }
  const keys2 = [];
  for (const key in object) {
    keys2.push(key);
  }
  for (let i = keys2.length - 1; i >= 0; i--) {
    const key = keys2[i];
    const result2 = iteratee2(object[key], key, object);
    if (result2 === false) {
      break;
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/forOwn.mjs
function forOwn(object, iteratee2 = identity) {
  if (object == null) {
    return object;
  }
  const iterable = Object(object);
  const keys$1 = keys(object);
  for (let i = 0; i < keys$1.length; ++i) {
    const key = keys$1[i];
    if (iteratee2(iterable[key], key, iterable) === false) {
      break;
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/forOwnRight.mjs
function forOwnRight(object, iteratee2 = identity) {
  if (object == null) {
    return object;
  }
  const iterable = Object(object);
  const keys$1 = keys(object);
  for (let i = keys$1.length - 1; i >= 0; --i) {
    const key = keys$1[i];
    if (iteratee2(iterable[key], key, iterable) === false) {
      break;
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/fromPairs.mjs
function fromPairs(pairs) {
  if (!isArrayLike(pairs)) {
    return {};
  }
  const result2 = {};
  for (let i = 0; i < pairs.length; i++) {
    const [key, value] = pairs[i];
    result2[key] = value;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/functions.mjs
function functions(object) {
  if (object == null) {
    return [];
  }
  return keys(object).filter((key) => typeof object[key] === "function");
}

// node_modules/es-toolkit/dist/compat/object/functionsIn.mjs
function functionsIn(object) {
  if (object == null) {
    return [];
  }
  const result2 = [];
  for (const key in object) {
    if (isFunction(object[key])) {
      result2.push(key);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/hasIn.mjs
function hasIn(object, path) {
  if (object == null) {
    return false;
  }
  let resolvedPath;
  if (Array.isArray(path)) {
    resolvedPath = path;
  } else if (typeof path === "string" && isDeepKey(path) && object[path] == null) {
    resolvedPath = toPath(path);
  } else {
    resolvedPath = [path];
  }
  if (resolvedPath.length === 0) {
    return false;
  }
  let current = object;
  for (let i = 0; i < resolvedPath.length; i++) {
    const key = resolvedPath[i];
    if (current == null || !(key in Object(current))) {
      const isSparseIndex = (Array.isArray(current) || isArguments(current)) && isIndex(key) && key < current.length;
      if (!isSparseIndex) {
        return false;
      }
    }
    current = current[key];
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/object/invert.mjs
function invert2(obj) {
  return invert(obj);
}

// node_modules/es-toolkit/dist/compat/object/invertBy.mjs
function invertBy(object, iteratee$1) {
  const result2 = {};
  if (isNil(object)) {
    return result2;
  }
  if (iteratee$1 == null) {
    iteratee$1 = identity;
  }
  const keys2 = Object.keys(object);
  const getString = iteratee(iteratee$1);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = object[key];
    const valueStr = getString(value);
    if (Array.isArray(result2[valueStr])) {
      result2[valueStr].push(key);
    } else {
      result2[valueStr] = [key];
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/mapKeys.mjs
function mapKeys2(object, getNewKey = identity) {
  if (object == null) {
    return {};
  }
  return mapKeys(object, iteratee(getNewKey));
}

// node_modules/es-toolkit/dist/compat/object/mapValues.mjs
function mapValues2(object, getNewValue = identity) {
  if (object == null) {
    return {};
  }
  return mapValues(object, iteratee(getNewValue));
}

// node_modules/es-toolkit/dist/compat/object/mergeWith.mjs
function mergeWith2(object, ...otherArgs) {
  const sources = otherArgs.slice(0, -1);
  const merge3 = otherArgs[otherArgs.length - 1];
  let result2 = object;
  for (let i = 0; i < sources.length; i++) {
    const source = sources[i];
    result2 = mergeWithDeep(result2, source, merge3, /* @__PURE__ */ new Map());
  }
  return result2;
}
function mergeWithDeep(target, source, merge3, stack) {
  if (isPrimitive(target)) {
    target = Object(target);
  }
  if (source == null || typeof source !== "object") {
    return target;
  }
  if (stack.has(source)) {
    return clone(stack.get(source));
  }
  stack.set(source, target);
  if (Array.isArray(source)) {
    source = source.slice();
    for (let i = 0; i < source.length; i++) {
      source[i] = source[i] ?? void 0;
    }
  }
  const sourceKeys = [...Object.keys(source), ...getSymbols(source)];
  for (let i = 0; i < sourceKeys.length; i++) {
    const key = sourceKeys[i];
    if (isUnsafeProperty(key)) {
      continue;
    }
    let sourceValue = source[key];
    let targetValue = target[key];
    if (isArguments(sourceValue)) {
      sourceValue = { ...sourceValue };
    }
    if (isArguments(targetValue)) {
      targetValue = { ...targetValue };
    }
    if (typeof Buffer !== "undefined" && Buffer.isBuffer(sourceValue)) {
      sourceValue = cloneDeep2(sourceValue);
    }
    if (Array.isArray(sourceValue)) {
      if (typeof targetValue === "object" && targetValue != null) {
        const cloned = [];
        const targetKeys = Reflect.ownKeys(targetValue);
        for (let i2 = 0; i2 < targetKeys.length; i2++) {
          const targetKey = targetKeys[i2];
          cloned[targetKey] = targetValue[targetKey];
        }
        targetValue = cloned;
      } else {
        targetValue = [];
      }
    }
    const merged = merge3(targetValue, sourceValue, key, target, source, stack);
    if (merged != null) {
      target[key] = merged;
    } else if (Array.isArray(sourceValue)) {
      target[key] = mergeWithDeep(targetValue, sourceValue, merge3, stack);
    } else if (isObjectLike(targetValue) && isObjectLike(sourceValue)) {
      target[key] = mergeWithDeep(targetValue, sourceValue, merge3, stack);
    } else if (targetValue == null && isPlainObject2(sourceValue)) {
      target[key] = mergeWithDeep({}, sourceValue, merge3, stack);
    } else if (targetValue == null && isTypedArray2(sourceValue)) {
      target[key] = cloneDeep2(sourceValue);
    } else if (targetValue === void 0 || sourceValue !== void 0) {
      target[key] = sourceValue;
    }
  }
  return target;
}

// node_modules/es-toolkit/dist/compat/object/merge.mjs
function merge2(object, ...sources) {
  return mergeWith2(object, ...sources, noop);
}

// node_modules/es-toolkit/dist/compat/object/omit.mjs
function omit2(obj, ...keysArr) {
  if (obj == null) {
    return {};
  }
  const result2 = cloneDeep(obj);
  for (let i = 0; i < keysArr.length; i++) {
    let keys2 = keysArr[i];
    switch (typeof keys2) {
      case "object": {
        if (!Array.isArray(keys2)) {
          keys2 = Array.from(keys2);
        }
        for (let j = 0; j < keys2.length; j++) {
          const key = keys2[j];
          unset(result2, key);
        }
        break;
      }
      case "string":
      case "symbol":
      case "number": {
        unset(result2, keys2);
        break;
      }
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/_internal/getSymbolsIn.mjs
function getSymbolsIn(object) {
  const result2 = [];
  while (object) {
    result2.push(...getSymbols(object));
    object = Object.getPrototypeOf(object);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/omitBy.mjs
function omitBy2(object, shouldOmit) {
  if (object == null) {
    return {};
  }
  const result2 = {};
  const predicate = iteratee(shouldOmit ?? identity2);
  const keys2 = isArrayLike(object) ? range(0, object.length) : [...keysIn(object), ...getSymbolsIn(object)];
  for (let i = 0; i < keys2.length; i++) {
    const key = isSymbol(keys2[i]) ? keys2[i] : keys2[i].toString();
    const value = object[key];
    if (!predicate(value, key, object)) {
      result2[key] = value;
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/pick.mjs
function pick2(obj, ...keysArr) {
  if (isNil2(obj)) {
    return {};
  }
  const result2 = {};
  for (let i = 0; i < keysArr.length; i++) {
    let keys2 = keysArr[i];
    switch (typeof keys2) {
      case "object": {
        if (!Array.isArray(keys2)) {
          if (isArrayLike(keys2)) {
            keys2 = Array.from(keys2);
          } else {
            keys2 = [keys2];
          }
        }
        break;
      }
      case "string":
      case "symbol":
      case "number": {
        keys2 = [keys2];
        break;
      }
    }
    for (const key of keys2) {
      const value = get(obj, key);
      if (value === void 0 && !has(obj, key)) {
        continue;
      }
      if (typeof key === "string" && Object.hasOwn(obj, key)) {
        result2[key] = value;
      } else {
        set2(result2, key, value);
      }
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/pickBy.mjs
function pickBy2(obj, shouldPick) {
  if (obj == null) {
    return {};
  }
  const predicate = iteratee(shouldPick ?? identity2);
  const result2 = {};
  const keys2 = isArrayLike(obj) ? range(0, obj.length) : [...keysIn(obj), ...getSymbolsIn(obj)];
  for (let i = 0; i < keys2.length; i++) {
    const key = isSymbol(keys2[i]) ? keys2[i] : keys2[i].toString();
    const value = obj[key];
    if (predicate(value, key, obj)) {
      result2[key] = value;
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/propertyOf.mjs
function propertyOf(object) {
  return function(path) {
    return get(object, path);
  };
}

// node_modules/es-toolkit/dist/compat/object/result.mjs
function result(object, path, defaultValue) {
  if (isKey(path, object)) {
    path = [path];
  } else if (!Array.isArray(path)) {
    path = toPath(toString(path));
  }
  const pathLength = Math.max(path.length, 1);
  for (let index = 0; index < pathLength; index++) {
    const value = object == null ? void 0 : object[toKey(path[index])];
    if (value === void 0) {
      return typeof defaultValue === "function" ? defaultValue.call(object) : defaultValue;
    }
    object = typeof value === "function" ? value.call(object) : value;
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/setWith.mjs
function setWith(obj, path, value, customizer) {
  let customizerFn;
  if (typeof customizer === "function") {
    customizerFn = customizer;
  } else {
    customizerFn = () => void 0;
  }
  return updateWith(obj, path, () => value, customizerFn);
}

// node_modules/es-toolkit/dist/compat/object/toDefaulted.mjs
function toDefaulted(object, ...sources) {
  const cloned = cloneDeep2(object);
  return defaults(cloned, ...sources);
}

// node_modules/es-toolkit/dist/compat/_internal/mapToEntries.mjs
function mapToEntries(map2) {
  const arr = new Array(map2.size);
  const keys2 = map2.keys();
  const values2 = map2.values();
  for (let i = 0; i < arr.length; i++) {
    arr[i] = [keys2.next().value, values2.next().value];
  }
  return arr;
}

// node_modules/es-toolkit/dist/compat/_internal/setToEntries.mjs
function setToEntries(set3) {
  const arr = new Array(set3.size);
  const values2 = set3.values();
  for (let i = 0; i < arr.length; i++) {
    const value = values2.next().value;
    arr[i] = [value, value];
  }
  return arr;
}

// node_modules/es-toolkit/dist/compat/object/toPairs.mjs
function toPairs(object) {
  if (object == null) {
    return [];
  }
  if (object instanceof Set) {
    return setToEntries(object);
  }
  if (object instanceof Map) {
    return mapToEntries(object);
  }
  const keys$1 = keys(object);
  const result2 = new Array(keys$1.length);
  for (let i = 0; i < keys$1.length; i++) {
    const key = keys$1[i];
    const value = object[key];
    result2[i] = [key, value];
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/toPairsIn.mjs
function toPairsIn(object) {
  if (object == null) {
    return [];
  }
  if (object instanceof Set) {
    return setToEntries(object);
  }
  if (object instanceof Map) {
    return mapToEntries(object);
  }
  const keys2 = keysIn(object);
  const result2 = new Array(keys2.length);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = object[key];
    result2[i] = [key, value];
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/predicate/isBuffer.mjs
function isBuffer2(x) {
  return isBuffer(x);
}

// node_modules/es-toolkit/dist/compat/object/transform.mjs
function transform(object, iteratee$1 = identity, accumulator) {
  const isArrayOrBufferOrTypedArray = Array.isArray(object) || isBuffer2(object) || isTypedArray2(object);
  iteratee$1 = iteratee(iteratee$1);
  if (accumulator == null) {
    if (isArrayOrBufferOrTypedArray) {
      accumulator = [];
    } else if (isObject(object) && isFunction(object.constructor)) {
      accumulator = Object.create(Object.getPrototypeOf(object));
    } else {
      accumulator = {};
    }
  }
  if (object == null) {
    return accumulator;
  }
  forEach(object, (value, key, object2) => iteratee$1(accumulator, value, key, object2));
  return accumulator;
}

// node_modules/es-toolkit/dist/compat/object/update.mjs
function update(obj, path, updater) {
  return updateWith(obj, path, updater, () => void 0);
}

// node_modules/es-toolkit/dist/compat/object/valuesIn.mjs
function valuesIn(object) {
  const keys2 = keysIn(object);
  const result2 = new Array(keys2.length);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    result2[i] = object[key];
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/predicate/isFunction.mjs
function isFunction2(value) {
  return typeof value === "function";
}

// node_modules/es-toolkit/dist/compat/predicate/isLength.mjs
function isLength2(value) {
  return Number.isSafeInteger(value) && value >= 0;
}

// node_modules/es-toolkit/dist/compat/predicate/isNative.mjs
var functionToString = Function.prototype.toString;
var REGEXP_SYNTAX_CHARS = /[\\^$.*+?()[\]{}|]/g;
var IS_NATIVE_FUNCTION_REGEXP = RegExp(`^${functionToString.call(Object.prototype.hasOwnProperty).replace(REGEXP_SYNTAX_CHARS, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?")}$`);
function isNative(value) {
  if (typeof value !== "function") {
    return false;
  }
  if (globalThis?.["__core-js_shared__"] != null) {
    throw new Error("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");
  }
  return IS_NATIVE_FUNCTION_REGEXP.test(functionToString.call(value));
}

// node_modules/es-toolkit/dist/compat/predicate/isNull.mjs
function isNull2(value) {
  return value === null;
}

// node_modules/es-toolkit/dist/compat/predicate/isUndefined.mjs
function isUndefined2(x) {
  return isUndefined(x);
}

// node_modules/es-toolkit/dist/compat/predicate/conformsTo.mjs
function conformsTo(target, source) {
  if (source == null) {
    return true;
  }
  if (target == null) {
    return Object.keys(source).length === 0;
  }
  const keys2 = Object.keys(source);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const predicate = source[key];
    const value = target[key];
    if (value === void 0 && !(key in target)) {
      return false;
    }
    if (typeof predicate === "function" && !predicate(value)) {
      return false;
    }
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/predicate/conforms.mjs
function conforms(source) {
  source = cloneDeep(source);
  return function(object) {
    return conformsTo(object, source);
  };
}

// node_modules/es-toolkit/dist/compat/predicate/isArrayBuffer.mjs
function isArrayBuffer2(value) {
  return isArrayBuffer(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isBoolean.mjs
function isBoolean2(value) {
  return typeof value === "boolean" || value instanceof Boolean;
}

// node_modules/es-toolkit/dist/compat/predicate/isDate.mjs
function isDate2(value) {
  return isDate(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isElement.mjs
function isElement(value) {
  return isObjectLike(value) && value.nodeType === 1 && !isPlainObject2(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isEmpty.mjs
function isEmpty(value) {
  if (value == null) {
    return true;
  }
  if (isArrayLike(value)) {
    if (typeof value.splice !== "function" && typeof value !== "string" && (typeof Buffer === "undefined" || !Buffer.isBuffer(value)) && !isTypedArray2(value) && !isArguments(value)) {
      return false;
    }
    return value.length === 0;
  }
  if (typeof value === "object") {
    if (value instanceof Map || value instanceof Set) {
      return value.size === 0;
    }
    const keys2 = Object.keys(value);
    if (isPrototype(value)) {
      return keys2.filter((x) => x !== "constructor").length === 0;
    }
    return keys2.length === 0;
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/predicate/isEqualWith.mjs
function isEqualWith2(a, b, areValuesEqual) {
  if (typeof areValuesEqual !== "function") {
    areValuesEqual = () => void 0;
  }
  return isEqualWith(a, b, (...args) => {
    const result2 = areValuesEqual(...args);
    if (result2 !== void 0) {
      return Boolean(result2);
    }
    if (a instanceof Map && b instanceof Map) {
      return isEqualWith2(Array.from(a), Array.from(b), after(2, areValuesEqual));
    }
    if (a instanceof Set && b instanceof Set) {
      return isEqualWith2(Array.from(a), Array.from(b), after(2, areValuesEqual));
    }
  });
}

// node_modules/es-toolkit/dist/compat/predicate/isError.mjs
function isError2(value) {
  return getTag(value) === "[object Error]";
}

// node_modules/es-toolkit/dist/compat/predicate/isFinite.mjs
function isFinite2(value) {
  return Number.isFinite(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isInteger.mjs
function isInteger(value) {
  return Number.isInteger(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isRegExp.mjs
function isRegExp2(value) {
  return isRegExp(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isSafeInteger.mjs
function isSafeInteger(value) {
  return Number.isSafeInteger(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isSet.mjs
function isSet2(value) {
  return isSet(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isWeakMap.mjs
function isWeakMap2(value) {
  return isWeakMap(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isWeakSet.mjs
function isWeakSet2(value) {
  return isWeakSet(value);
}

// node_modules/es-toolkit/dist/compat/string/capitalize.mjs
function capitalize2(str) {
  return capitalize(toString(str));
}

// node_modules/es-toolkit/dist/compat/util/bindAll.mjs
function bindAll(object, ...methodNames) {
  if (object == null) {
    return object;
  }
  if (!isObject(object)) {
    return object;
  }
  if (isArray(object) && methodNames.length === 0) {
    return object;
  }
  const methods = [];
  for (let i = 0; i < methodNames.length; i++) {
    const name = methodNames[i];
    if (isArray(name)) {
      methods.push(...name);
    } else if (name && typeof name === "object" && "length" in name) {
      methods.push(...Array.from(name));
    } else {
      methods.push(name);
    }
  }
  if (methods.length === 0) {
    return object;
  }
  for (let i = 0; i < methods.length; i++) {
    const key = methods[i];
    const stringKey = toString(key);
    const func = object[stringKey];
    if (isFunction(func)) {
      object[stringKey] = func.bind(object);
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/_internal/normalizeForCase.mjs
function normalizeForCase(str) {
  if (typeof str !== "string") {
    str = toString(str);
  }
  return str.replace(/['\u2019]/g, "");
}

// node_modules/es-toolkit/dist/compat/string/camelCase.mjs
function camelCase2(str) {
  return camelCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/deburr.mjs
function deburr2(str) {
  return deburr(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/endsWith.mjs
function endsWith(str, target, position) {
  if (str == null || target == null) {
    return false;
  }
  if (position == null) {
    position = str.length;
  }
  return str.endsWith(target, position);
}

// node_modules/es-toolkit/dist/compat/string/escape.mjs
function escape3(string) {
  return escape2(toString(string));
}

// node_modules/es-toolkit/dist/compat/string/escapeRegExp.mjs
function escapeRegExp2(str) {
  return escapeRegExp(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/kebabCase.mjs
function kebabCase2(str) {
  return kebabCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/lowerCase.mjs
function lowerCase2(str) {
  return lowerCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/lowerFirst.mjs
function lowerFirst2(str) {
  return lowerFirst(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/pad.mjs
function pad2(str, length, chars) {
  return pad(toString(str), length, chars);
}

// node_modules/es-toolkit/dist/compat/string/padEnd.mjs
function padEnd(str, length = 0, chars = " ") {
  return toString(str).padEnd(length, chars);
}

// node_modules/es-toolkit/dist/compat/string/padStart.mjs
function padStart(str, length = 0, chars = " ") {
  return toString(str).padStart(length, chars);
}

// node_modules/es-toolkit/dist/compat/string/repeat.mjs
function repeat(str, n, guard) {
  if (guard ? isIterateeCall(str, n, guard) : n === void 0) {
    n = 1;
  } else {
    n = toInteger(n);
  }
  return toString(str).repeat(n);
}

// node_modules/es-toolkit/dist/compat/string/replace.mjs
function replace(target, pattern, replacement) {
  if (arguments.length < 3) {
    return toString(target);
  }
  return toString(target).replace(pattern, replacement);
}

// node_modules/es-toolkit/dist/compat/string/snakeCase.mjs
function snakeCase2(str) {
  return snakeCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/split.mjs
function split(string, separator, limit) {
  return toString(string).split(separator, limit);
}

// node_modules/es-toolkit/dist/compat/string/startCase.mjs
function startCase2(str) {
  const words$1 = words(normalizeForCase(str).trim());
  let result2 = "";
  for (let i = 0; i < words$1.length; i++) {
    const word = words$1[i];
    if (result2) {
      result2 += " ";
    }
    if (word === word.toUpperCase()) {
      result2 += word;
    } else {
      result2 += word[0].toUpperCase() + word.slice(1).toLowerCase();
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/string/startsWith.mjs
function startsWith(str, target, position) {
  if (str == null || target == null) {
    return false;
  }
  if (position == null) {
    position = 0;
  }
  return str.startsWith(target, position);
}

// node_modules/es-toolkit/dist/compat/string/template.mjs
var esTemplateRegExp = /\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g;
var unEscapedRegExp = /['\n\r\u2028\u2029\\]/g;
var noMatchExp = /($^)/;
var escapeMap = /* @__PURE__ */ new Map([
  ["\\", "\\"],
  ["'", "'"],
  ["\n", "n"],
  ["\r", "r"],
  ["\u2028", "u2028"],
  ["\u2029", "u2029"]
]);
function escapeString(match) {
  return `\\${escapeMap.get(match)}`;
}
var templateSettings = {
  escape: /<%-([\s\S]+?)%>/g,
  evaluate: /<%([\s\S]+?)%>/g,
  interpolate: /<%=([\s\S]+?)%>/g,
  variable: "",
  imports: {
    _: {
      escape: escape3,
      template
    }
  }
};
function template(string, options, guard) {
  string = toString(string);
  if (guard) {
    options = templateSettings;
  }
  options = defaults({ ...options }, templateSettings);
  const delimitersRegExp = new RegExp([
    options.escape?.source ?? noMatchExp.source,
    options.interpolate?.source ?? noMatchExp.source,
    options.interpolate ? esTemplateRegExp.source : noMatchExp.source,
    options.evaluate?.source ?? noMatchExp.source,
    "$"
  ].join("|"), "g");
  let lastIndex = 0;
  let isEvaluated = false;
  let source = `__p += ''`;
  for (const match of string.matchAll(delimitersRegExp)) {
    const [fullMatch, escapeValue, interpolateValue, esTemplateValue, evaluateValue] = match;
    const { index } = match;
    source += ` + '${string.slice(lastIndex, index).replace(unEscapedRegExp, escapeString)}'`;
    if (escapeValue) {
      source += ` + _.escape(${escapeValue})`;
    }
    if (interpolateValue) {
      source += ` + ((${interpolateValue}) == null ? '' : ${interpolateValue})`;
    } else if (esTemplateValue) {
      source += ` + ((${esTemplateValue}) == null ? '' : ${esTemplateValue})`;
    }
    if (evaluateValue) {
      source += `;
${evaluateValue};
 __p += ''`;
      isEvaluated = true;
    }
    lastIndex = index + fullMatch.length;
  }
  const imports = defaults({ ...options.imports }, templateSettings.imports);
  const importsKeys = Object.keys(imports);
  const importValues = Object.values(imports);
  const sourceURL = `//# sourceURL=${options.sourceURL ? String(options.sourceURL).replace(/[\r\n]/g, " ") : `es-toolkit.templateSource[${Date.now()}]`}
`;
  const compiledFunction = `function(${options.variable || "obj"}) {
    let __p = '';
    ${options.variable ? "" : "if (obj == null) { obj = {}; }"}
    ${isEvaluated ? `function print() { __p += Array.prototype.join.call(arguments, ''); }` : ""}
    ${options.variable ? source : `with(obj) {
${source}
}`}
    return __p;
  }`;
  const result2 = attempt2(() => new Function(...importsKeys, `${sourceURL}return ${compiledFunction}`)(...importValues));
  result2.source = compiledFunction;
  if (result2 instanceof Error) {
    throw result2;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/string/toLower.mjs
function toLower(value) {
  return toString(value).toLowerCase();
}

// node_modules/es-toolkit/dist/compat/string/toUpper.mjs
function toUpper(value) {
  return toString(value).toUpperCase();
}

// node_modules/es-toolkit/dist/compat/string/trim.mjs
function trim2(str, chars, guard) {
  if (str == null) {
    return "";
  }
  if (guard != null || chars == null) {
    return str.toString().trim();
  }
  switch (typeof chars) {
    case "object": {
      if (Array.isArray(chars)) {
        return trim(str, chars.flatMap((x) => x.toString().split("")));
      } else {
        return trim(str, chars.toString().split(""));
      }
    }
    default: {
      return trim(str, chars.toString().split(""));
    }
  }
}

// node_modules/es-toolkit/dist/compat/string/trimEnd.mjs
function trimEnd2(str, chars, guard) {
  if (str == null) {
    return "";
  }
  if (guard != null || chars == null) {
    return str.toString().trimEnd();
  }
  return trimEnd(str, chars.toString().split(""));
}

// node_modules/es-toolkit/dist/compat/string/trimStart.mjs
function trimStart2(str, chars, guard) {
  if (str == null) {
    return "";
  }
  if (guard != null || chars == null) {
    return str.toString().trimStart();
  }
  return trimStart(str, chars.toString().split(""));
}

// node_modules/es-toolkit/dist/compat/string/truncate.mjs
var regexMultiByte = /[\u200d\ud800-\udfff\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff\ufe0e\ufe0f]/;
function truncate(string, options) {
  string = string != null ? `${string}` : "";
  let length = 30;
  let omission = "...";
  if (isObject(options)) {
    length = parseLength(options.length);
    omission = "omission" in options ? `${options.omission}` : "...";
  }
  let i = string.length;
  const lengthOmission = Array.from(omission).length;
  const lengthBase = Math.max(length - lengthOmission, 0);
  let strArray = void 0;
  const unicode = regexMultiByte.test(string);
  if (unicode) {
    strArray = Array.from(string);
    i = strArray.length;
  }
  if (length >= i) {
    return string;
  }
  if (i <= lengthOmission) {
    return omission;
  }
  let base = strArray === void 0 ? string.slice(0, lengthBase) : strArray?.slice(0, lengthBase).join("");
  const separator = options?.separator;
  if (!separator) {
    base += omission;
    return base;
  }
  const search = separator instanceof RegExp ? separator.source : separator;
  const flags = "u" + (separator instanceof RegExp ? separator.flags.replace("u", "") : "");
  const withoutSeparator = new RegExp(`(?<result>.*(?:(?!${search}).))(?:${search})`, flags).exec(base);
  return (!withoutSeparator?.groups ? base : withoutSeparator.groups.result) + omission;
}
function parseLength(length) {
  if (length == null) {
    return 30;
  }
  if (length <= 0) {
    return 0;
  }
  return length;
}

// node_modules/es-toolkit/dist/compat/string/unescape.mjs
function unescape3(str) {
  return unescape2(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/upperCase.mjs
function upperCase2(str) {
  return upperCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/upperFirst.mjs
function upperFirst2(str) {
  return upperFirst(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/words.mjs
var rNonCharLatin = "\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\xd7\\xf7";
var rUnicodeUpper = "\\p{Lu}";
var rUnicodeLower = "\\p{Ll}";
var rMisc = "(?:[\\p{Lm}\\p{Lo}]\\p{M}*)";
var rNumber = "\\d";
var rUnicodeOptContrLower = "(?:['’](?:d|ll|m|re|s|t|ve))?";
var rUnicodeOptContrUpper = "(?:['’](?:D|LL|M|RE|S|T|VE))?";
var rUnicodeBreak = `[\\p{Z}\\p{P}${rNonCharLatin}]`;
var rUnicodeMiscUpper = `(?:${rUnicodeUpper}|${rMisc})`;
var rUnicodeMiscLower = `(?:${rUnicodeLower}|${rMisc})`;
var rUnicodeWord = RegExp([
  `${rUnicodeUpper}?${rUnicodeLower}+${rUnicodeOptContrLower}(?=${rUnicodeBreak}|${rUnicodeUpper}|$)`,
  `${rUnicodeMiscUpper}+${rUnicodeOptContrUpper}(?=${rUnicodeBreak}|${rUnicodeUpper}${rUnicodeMiscLower}|$)`,
  `${rUnicodeUpper}?${rUnicodeMiscLower}+${rUnicodeOptContrLower}`,
  `${rUnicodeUpper}+${rUnicodeOptContrUpper}`,
  `${rNumber}*(?:1ST|2ND|3RD|(?![123])${rNumber}TH)(?=\\b|[a-z_])`,
  `${rNumber}*(?:1st|2nd|3rd|(?![123])${rNumber}th)(?=\\b|[A-Z_])`,
  `${rNumber}+`,
  "\\p{Emoji_Presentation}",
  "\\p{Extended_Pictographic}"
].join("|"), "gu");
function words2(str, pattern = rUnicodeWord, guard) {
  const input = toString(str);
  if (guard) {
    pattern = rUnicodeWord;
  }
  if (typeof pattern === "number") {
    pattern = pattern.toString();
  }
  const words3 = Array.from(input.match(pattern) ?? []);
  return words3.filter((x) => x !== "");
}

// node_modules/es-toolkit/dist/compat/util/cond.mjs
function cond(pairs) {
  const length = pairs.length;
  const processedPairs = pairs.map((pair) => {
    const predicate = pair[0];
    const func = pair[1];
    if (!isFunction(func)) {
      throw new TypeError("Expected a function");
    }
    return [iteratee(predicate), func];
  });
  return function(...args) {
    for (let i = 0; i < length; i++) {
      const pair = processedPairs[i];
      const predicate = pair[0];
      const func = pair[1];
      if (predicate.apply(this, args)) {
        return func.apply(this, args);
      }
    }
  };
}

// node_modules/es-toolkit/dist/compat/util/constant.mjs
function constant(value) {
  return () => value;
}

// node_modules/es-toolkit/dist/compat/util/defaultTo.mjs
function defaultTo(value, defaultValue) {
  if (value == null || Number.isNaN(value)) {
    return defaultValue;
  }
  return value;
}

// node_modules/es-toolkit/dist/compat/util/gt.mjs
function gt(value, other) {
  if (typeof value === "string" && typeof other === "string") {
    return value > other;
  }
  return toNumber(value) > toNumber(other);
}

// node_modules/es-toolkit/dist/compat/util/gte.mjs
function gte(value, other) {
  if (typeof value === "string" && typeof other === "string") {
    return value >= other;
  }
  return toNumber(value) >= toNumber(other);
}

// node_modules/es-toolkit/dist/compat/util/invoke.mjs
function invoke(object, path, ...args) {
  args = args.flat(1);
  if (object == null) {
    return;
  }
  switch (typeof path) {
    case "string": {
      if (typeof object === "object" && Object.hasOwn(object, path)) {
        return invokeImpl(object, [path], args);
      }
      return invokeImpl(object, toPath(path), args);
    }
    case "number":
    case "symbol": {
      return invokeImpl(object, [path], args);
    }
    default: {
      if (Array.isArray(path)) {
        return invokeImpl(object, path, args);
      } else {
        return invokeImpl(object, [path], args);
      }
    }
  }
}
function invokeImpl(object, path, args) {
  const parent = get(object, path.slice(0, -1), object);
  if (parent == null) {
    return void 0;
  }
  let lastKey = last2(path);
  const lastValue = lastKey?.valueOf();
  if (typeof lastValue === "number") {
    lastKey = toKey(lastValue);
  } else {
    lastKey = String(lastKey);
  }
  const func = get(parent, lastKey);
  return func?.apply(parent, args);
}

// node_modules/es-toolkit/dist/compat/util/lt.mjs
function lt(value, other) {
  if (typeof value === "string" && typeof other === "string") {
    return value < other;
  }
  return toNumber(value) < toNumber(other);
}

// node_modules/es-toolkit/dist/compat/util/lte.mjs
function lte(value, other) {
  if (typeof value === "string" && typeof other === "string") {
    return value <= other;
  }
  return toNumber(value) <= toNumber(other);
}

// node_modules/es-toolkit/dist/compat/util/method.mjs
function method(path, ...args) {
  return function(object) {
    return invoke(object, path, args);
  };
}

// node_modules/es-toolkit/dist/compat/util/methodOf.mjs
function methodOf(object, ...args) {
  return function(path) {
    return invoke(object, path, args);
  };
}

// node_modules/es-toolkit/dist/compat/util/now.mjs
function now() {
  return Date.now();
}

// node_modules/es-toolkit/dist/compat/util/over.mjs
function over(...iteratees) {
  if (iteratees.length === 1 && Array.isArray(iteratees[0])) {
    iteratees = iteratees[0];
  }
  const funcs = iteratees.map((item) => iteratee(item));
  return function(...args) {
    return funcs.map((func) => func.apply(this, args));
  };
}

// node_modules/es-toolkit/dist/compat/util/overEvery.mjs
function overEvery(...predicates) {
  return function(...values2) {
    for (let i = 0; i < predicates.length; ++i) {
      const predicate = predicates[i];
      if (!Array.isArray(predicate)) {
        if (!iteratee(predicate).apply(this, values2)) {
          return false;
        }
        continue;
      }
      for (let j = 0; j < predicate.length; ++j) {
        if (!iteratee(predicate[j]).apply(this, values2)) {
          return false;
        }
      }
    }
    return true;
  };
}

// node_modules/es-toolkit/dist/compat/util/overSome.mjs
function overSome(...predicates) {
  return function(...values2) {
    for (let i = 0; i < predicates.length; ++i) {
      const predicate = predicates[i];
      if (!Array.isArray(predicate)) {
        if (iteratee(predicate).apply(this, values2)) {
          return true;
        }
        continue;
      }
      for (let j = 0; j < predicate.length; ++j) {
        if (iteratee(predicate[j]).apply(this, values2)) {
          return true;
        }
      }
    }
    return false;
  };
}

// node_modules/es-toolkit/dist/compat/util/stubArray.mjs
function stubArray() {
  return [];
}

// node_modules/es-toolkit/dist/compat/util/stubFalse.mjs
function stubFalse() {
  return false;
}

// node_modules/es-toolkit/dist/compat/util/stubObject.mjs
function stubObject() {
  return {};
}

// node_modules/es-toolkit/dist/compat/util/stubString.mjs
function stubString() {
  return "";
}

// node_modules/es-toolkit/dist/compat/util/stubTrue.mjs
function stubTrue() {
  return true;
}

// node_modules/es-toolkit/dist/compat/_internal/MAX_ARRAY_LENGTH.mjs
var MAX_ARRAY_LENGTH4 = 4294967295;

// node_modules/es-toolkit/dist/compat/util/toLength.mjs
function toLength(value) {
  if (value == null) {
    return 0;
  }
  const length = Math.floor(Number(value));
  return clamp3(length, 0, MAX_ARRAY_LENGTH4);
}

// node_modules/es-toolkit/dist/compat/util/toPlainObject.mjs
function toPlainObject(value) {
  const plainObject = {};
  const valueKeys = keysIn(value);
  for (let i = 0; i < valueKeys.length; i++) {
    const key = valueKeys[i];
    const objValue = value[key];
    if (key === "__proto__") {
      Object.defineProperty(plainObject, key, {
        configurable: true,
        enumerable: true,
        value: objValue,
        writable: true
      });
    } else {
      plainObject[key] = objValue;
    }
  }
  return plainObject;
}

// node_modules/es-toolkit/dist/compat/_internal/MAX_SAFE_INTEGER.mjs
var MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER;

// node_modules/es-toolkit/dist/compat/util/toSafeInteger.mjs
function toSafeInteger(value) {
  if (value == null) {
    return 0;
  }
  return clamp3(toInteger(value), -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER);
}

// node_modules/es-toolkit/dist/compat/util/uniqueId.mjs
var idCounter = 0;
function uniqueId(prefix = "") {
  const id = ++idCounter;
  return `${prefix}${id}`;
}

// node_modules/es-toolkit/dist/compat/compat.mjs
var compat_exports = {};
__export(compat_exports, {
  add: () => add,
  after: () => after2,
  ary: () => ary2,
  assign: () => assign,
  assignIn: () => assignIn,
  assignInWith: () => assignInWith,
  assignWith: () => assignWith,
  at: () => at2,
  attempt: () => attempt2,
  before: () => before2,
  bind: () => bind,
  bindAll: () => bindAll,
  bindKey: () => bindKey,
  camelCase: () => camelCase2,
  capitalize: () => capitalize2,
  castArray: () => castArray,
  ceil: () => ceil,
  chunk: () => chunk2,
  clamp: () => clamp3,
  clone: () => clone2,
  cloneDeep: () => cloneDeep2,
  cloneDeepWith: () => cloneDeepWith2,
  cloneWith: () => cloneWith,
  compact: () => compact2,
  concat: () => concat,
  cond: () => cond,
  conforms: () => conforms,
  conformsTo: () => conformsTo,
  constant: () => constant,
  countBy: () => countBy2,
  create: () => create,
  curry: () => curry2,
  curryRight: () => curryRight2,
  debounce: () => debounce3,
  deburr: () => deburr2,
  defaultTo: () => defaultTo,
  defaults: () => defaults,
  defaultsDeep: () => defaultsDeep,
  defer: () => defer,
  delay: () => delay2,
  difference: () => difference2,
  differenceBy: () => differenceBy2,
  differenceWith: () => differenceWith2,
  divide: () => divide,
  drop: () => drop2,
  dropRight: () => dropRight2,
  dropRightWhile: () => dropRightWhile2,
  dropWhile: () => dropWhile2,
  each: () => forEach,
  eachRight: () => forEachRight2,
  endsWith: () => endsWith,
  eq: () => eq,
  escape: () => escape3,
  escapeRegExp: () => escapeRegExp2,
  every: () => every,
  extend: () => assignIn,
  extendWith: () => assignInWith,
  fill: () => fill2,
  filter: () => filter,
  find: () => find,
  findIndex: () => findIndex,
  findKey: () => findKey2,
  findLast: () => findLast,
  findLastIndex: () => findLastIndex,
  findLastKey: () => findLastKey,
  first: () => head2,
  flatMap: () => flatMap2,
  flatMapDeep: () => flatMapDeep2,
  flatMapDepth: () => flatMapDepth,
  flatten: () => flatten2,
  flattenDeep: () => flattenDeep2,
  flattenDepth: () => flattenDepth,
  flip: () => flip,
  floor: () => floor,
  flow: () => flow2,
  flowRight: () => flowRight2,
  forEach: () => forEach,
  forEachRight: () => forEachRight2,
  forIn: () => forIn,
  forInRight: () => forInRight,
  forOwn: () => forOwn,
  forOwnRight: () => forOwnRight,
  fromPairs: () => fromPairs,
  functions: () => functions,
  functionsIn: () => functionsIn,
  get: () => get,
  groupBy: () => groupBy2,
  gt: () => gt,
  gte: () => gte,
  has: () => has,
  hasIn: () => hasIn,
  head: () => head2,
  identity: () => identity2,
  inRange: () => inRange2,
  includes: () => includes,
  indexOf: () => indexOf,
  initial: () => initial2,
  intersection: () => intersection2,
  intersectionBy: () => intersectionBy2,
  intersectionWith: () => intersectionWith2,
  invert: () => invert2,
  invertBy: () => invertBy,
  invoke: () => invoke,
  invokeMap: () => invokeMap,
  isArguments: () => isArguments,
  isArray: () => isArray,
  isArrayBuffer: () => isArrayBuffer2,
  isArrayLike: () => isArrayLike,
  isArrayLikeObject: () => isArrayLikeObject,
  isBoolean: () => isBoolean2,
  isBuffer: () => isBuffer2,
  isDate: () => isDate2,
  isElement: () => isElement,
  isEmpty: () => isEmpty,
  isEqual: () => isEqual,
  isEqualWith: () => isEqualWith2,
  isError: () => isError2,
  isFinite: () => isFinite2,
  isFunction: () => isFunction2,
  isInteger: () => isInteger,
  isLength: () => isLength2,
  isMap: () => isMap2,
  isMatch: () => isMatch,
  isMatchWith: () => isMatchWith,
  isNaN: () => isNaN2,
  isNative: () => isNative,
  isNil: () => isNil2,
  isNull: () => isNull2,
  isNumber: () => isNumber,
  isObject: () => isObject,
  isObjectLike: () => isObjectLike,
  isPlainObject: () => isPlainObject2,
  isRegExp: () => isRegExp2,
  isSafeInteger: () => isSafeInteger,
  isSet: () => isSet2,
  isString: () => isString2,
  isSymbol: () => isSymbol,
  isTypedArray: () => isTypedArray2,
  isUndefined: () => isUndefined2,
  isWeakMap: () => isWeakMap2,
  isWeakSet: () => isWeakSet2,
  iteratee: () => iteratee,
  join: () => join,
  kebabCase: () => kebabCase2,
  keyBy: () => keyBy2,
  keys: () => keys,
  keysIn: () => keysIn,
  last: () => last2,
  lastIndexOf: () => lastIndexOf,
  lowerCase: () => lowerCase2,
  lowerFirst: () => lowerFirst2,
  lt: () => lt,
  lte: () => lte,
  map: () => map,
  mapKeys: () => mapKeys2,
  mapValues: () => mapValues2,
  matches: () => matches,
  matchesProperty: () => matchesProperty,
  max: () => max,
  maxBy: () => maxBy2,
  mean: () => mean2,
  meanBy: () => meanBy2,
  memoize: () => memoize2,
  merge: () => merge2,
  mergeWith: () => mergeWith2,
  method: () => method,
  methodOf: () => methodOf,
  min: () => min,
  minBy: () => minBy2,
  multiply: () => multiply,
  negate: () => negate2,
  noop: () => noop2,
  now: () => now,
  nth: () => nth,
  nthArg: () => nthArg,
  omit: () => omit2,
  omitBy: () => omitBy2,
  once: () => once2,
  orderBy: () => orderBy2,
  over: () => over,
  overArgs: () => overArgs,
  overEvery: () => overEvery,
  overSome: () => overSome,
  pad: () => pad2,
  padEnd: () => padEnd,
  padStart: () => padStart,
  parseInt: () => parseInt2,
  partial: () => partial2,
  partialRight: () => partialRight2,
  partition: () => partition2,
  pick: () => pick2,
  pickBy: () => pickBy2,
  property: () => property,
  propertyOf: () => propertyOf,
  pull: () => pull2,
  pullAll: () => pullAll,
  pullAllBy: () => pullAllBy,
  pullAllWith: () => pullAllWith,
  pullAt: () => pullAt2,
  random: () => random2,
  range: () => range2,
  rangeRight: () => rangeRight2,
  rearg: () => rearg,
  reduce: () => reduce,
  reduceRight: () => reduceRight,
  reject: () => reject,
  remove: () => remove3,
  repeat: () => repeat,
  replace: () => replace,
  rest: () => rest2,
  result: () => result,
  reverse: () => reverse,
  round: () => round2,
  sample: () => sample2,
  sampleSize: () => sampleSize2,
  set: () => set2,
  setWith: () => setWith,
  shuffle: () => shuffle2,
  size: () => size,
  slice: () => slice,
  snakeCase: () => snakeCase2,
  some: () => some,
  sortBy: () => sortBy2,
  sortedIndex: () => sortedIndex,
  sortedIndexBy: () => sortedIndexBy,
  sortedIndexOf: () => sortedIndexOf,
  sortedLastIndex: () => sortedLastIndex,
  sortedLastIndexBy: () => sortedLastIndexBy,
  sortedLastIndexOf: () => sortedLastIndexOf,
  split: () => split,
  spread: () => spread2,
  startCase: () => startCase2,
  startsWith: () => startsWith,
  stubArray: () => stubArray,
  stubFalse: () => stubFalse,
  stubObject: () => stubObject,
  stubString: () => stubString,
  stubTrue: () => stubTrue,
  subtract: () => subtract,
  sum: () => sum2,
  sumBy: () => sumBy2,
  tail: () => tail2,
  take: () => take2,
  takeRight: () => takeRight2,
  takeRightWhile: () => takeRightWhile2,
  takeWhile: () => takeWhile2,
  template: () => template,
  templateSettings: () => templateSettings,
  throttle: () => throttle2,
  times: () => times,
  toArray: () => toArray2,
  toDefaulted: () => toDefaulted,
  toFinite: () => toFinite,
  toInteger: () => toInteger,
  toLength: () => toLength,
  toLower: () => toLower,
  toNumber: () => toNumber,
  toPairs: () => toPairs,
  toPairsIn: () => toPairsIn,
  toPath: () => toPath,
  toPlainObject: () => toPlainObject,
  toSafeInteger: () => toSafeInteger,
  toString: () => toString,
  toUpper: () => toUpper,
  transform: () => transform,
  trim: () => trim2,
  trimEnd: () => trimEnd2,
  trimStart: () => trimStart2,
  truncate: () => truncate,
  unary: () => unary2,
  unescape: () => unescape3,
  union: () => union2,
  unionBy: () => unionBy2,
  unionWith: () => unionWith2,
  uniq: () => uniq2,
  uniqBy: () => uniqBy2,
  uniqWith: () => uniqWith2,
  uniqueId: () => uniqueId,
  unset: () => unset,
  unzip: () => unzip2,
  unzipWith: () => unzipWith2,
  update: () => update,
  updateWith: () => updateWith,
  upperCase: () => upperCase2,
  upperFirst: () => upperFirst2,
  values: () => values,
  valuesIn: () => valuesIn,
  without: () => without2,
  words: () => words2,
  wrap: () => wrap,
  xor: () => xor2,
  xorBy: () => xorBy2,
  xorWith: () => xorWith2,
  zip: () => zip2,
  zipObject: () => zipObject2,
  zipObjectDeep: () => zipObjectDeep,
  zipWith: () => zipWith2
});

// node_modules/es-toolkit/dist/compat/toolkit.mjs
var toolkit = (value) => {
  return value;
};
Object.assign(toolkit, compat_exports);
toolkit.partial.placeholder = toolkit;
toolkit.partialRight.placeholder = toolkit;

// node_modules/@inertiajs/react/dist/index.esm.js
var import_react9 = __toESM(require_react());
var import_react10 = __toESM(require_react());
var import_react11 = __toESM(require_react());
var import_react12 = __toESM(require_react());
var import_react13 = __toESM(require_react());
var headContext = (0, import_react3.createContext)(void 0);
headContext.displayName = "InertiaHeadContext";
var HeadContext_default = headContext;
var pageContext = (0, import_react4.createContext)(void 0);
pageContext.displayName = "InertiaPageContext";
var PageContext_default = pageContext;
var currentIsInitialPage = true;
var routerIsInitialized = false;
var swapComponent = async () => {
  currentIsInitialPage = false;
};
function App({
  children,
  initialPage,
  initialComponent,
  resolveComponent,
  titleCallback,
  onHeadUpdate
}) {
  const [current, setCurrent] = (0, import_react2.useState)({
    component: initialComponent || null,
    page: initialPage,
    key: null
  });
  const headManager = (0, import_react2.useMemo)(() => {
    return createHeadManager(
      typeof window === "undefined",
      titleCallback || ((title) => title),
      onHeadUpdate || (() => {
      })
    );
  }, []);
  if (!routerIsInitialized) {
    router.init({
      initialPage,
      resolveComponent,
      swapComponent: async (args) => swapComponent(args)
    });
    routerIsInitialized = true;
  }
  (0, import_react2.useEffect)(() => {
    swapComponent = async ({ component, page: page2, preserveState }) => {
      if (currentIsInitialPage) {
        currentIsInitialPage = false;
        return;
      }
      setCurrent((current2) => ({
        component,
        page: page2,
        key: preserveState ? current2.key : Date.now()
      }));
    };
    router.on("navigate", () => headManager.forceUpdate());
  }, []);
  if (!current.component) {
    return (0, import_react2.createElement)(
      HeadContext_default.Provider,
      { value: headManager },
      (0, import_react2.createElement)(PageContext_default.Provider, { value: current.page }, null)
    );
  }
  const renderChildren = children || (({ Component, props, key }) => {
    const child = (0, import_react2.createElement)(Component, { key, ...props });
    if (typeof Component.layout === "function") {
      return Component.layout(child);
    }
    if (Array.isArray(Component.layout)) {
      return Component.layout.concat(child).reverse().reduce((children2, Layout) => (0, import_react2.createElement)(Layout, { children: children2, ...props }));
    }
    return child;
  });
  return (0, import_react2.createElement)(
    HeadContext_default.Provider,
    { value: headManager },
    (0, import_react2.createElement)(
      PageContext_default.Provider,
      { value: current.page },
      renderChildren({
        Component: current.component,
        key: current.key,
        props: current.page.props
      })
    )
  );
}
App.displayName = "Inertia";
async function createInertiaApp({
  id = "app",
  resolve,
  setup,
  title,
  progress: progress3 = {},
  page: page2,
  render: render2
}) {
  const isServer2 = typeof window === "undefined";
  const el = isServer2 ? null : document.getElementById(id);
  const initialPage = page2 || JSON.parse(el.dataset.page);
  const resolveComponent = (name) => Promise.resolve(resolve(name)).then((module) => module.default || module);
  let head3 = [];
  const reactApp = await Promise.all([
    resolveComponent(initialPage.component),
    router.decryptHistory().catch(() => {
    })
  ]).then(([initialComponent]) => {
    return setup({
      // @ts-expect-error
      el,
      App,
      props: {
        initialPage,
        initialComponent,
        resolveComponent,
        titleCallback: title,
        onHeadUpdate: isServer2 ? (elements) => head3 = elements : null
      }
    });
  });
  if (!isServer2 && progress3) {
    setupProgress(progress3);
  }
  if (isServer2) {
    const body = await render2(
      (0, import_react.createElement)(
        "div",
        {
          id,
          "data-page": JSON.stringify(initialPage)
        },
        // @ts-expect-error
        reactApp
      )
    );
    return { head: head3, body };
  }
}
function usePage() {
  const page2 = (0, import_react6.useContext)(PageContext_default);
  if (!page2) {
    throw new Error("usePage must be used within the Inertia component");
  }
  return page2;
}
var urlWithoutHash2 = (url) => {
  url = new URL(url.href);
  url.hash = "";
  return url;
};
var isSameUrlWithoutHash2 = (url1, url2) => {
  return urlWithoutHash2(url1).href === urlWithoutHash2(url2).href;
};
var Deferred = ({ children, data, fallback }) => {
  if (!data) {
    throw new Error("`<Deferred>` requires a `data` prop to be a string or array of strings");
  }
  const [loaded, setLoaded] = (0, import_react5.useState)(false);
  const pageProps = usePage().props;
  const keys2 = (0, import_react5.useMemo)(() => Array.isArray(data) ? data : [data], [data]);
  (0, import_react5.useEffect)(() => {
    const removeListener = router3.on("start", (e) => {
      const isPartialVisit = e.detail.visit.only.length > 0 || e.detail.visit.except.length > 0;
      const isReloadingKey = e.detail.visit.only.find((key) => keys2.includes(key));
      if (isSameUrlWithoutHash2(e.detail.visit.url, window.location) && (!isPartialVisit || isReloadingKey)) {
        setLoaded(false);
      }
    });
    return () => {
      removeListener();
    };
  }, []);
  (0, import_react5.useEffect)(() => {
    setLoaded(keys2.every((key) => pageProps[key] !== void 0));
  }, [pageProps, keys2]);
  return loaded ? children : fallback;
};
Deferred.displayName = "InertiaDeferred";
var Deferred_default = Deferred;
var Head = function({ children, title }) {
  const headManager = (0, import_react7.useContext)(HeadContext_default);
  const provider = (0, import_react7.useMemo)(() => headManager.createProvider(), [headManager]);
  const isServer2 = typeof window === "undefined";
  (0, import_react7.useEffect)(() => {
    provider.reconnect();
    provider.update(renderNodes(children));
    return () => {
      provider.disconnect();
    };
  }, [provider, children, title]);
  function isUnaryTag(node) {
    return [
      "area",
      "base",
      "br",
      "col",
      "embed",
      "hr",
      "img",
      "input",
      "keygen",
      "link",
      "meta",
      "param",
      "source",
      "track",
      "wbr"
    ].indexOf(node.type) > -1;
  }
  function renderTagStart(node) {
    const attrs = Object.keys(node.props).reduce((carry, name) => {
      if (["head-key", "children", "dangerouslySetInnerHTML"].includes(name)) {
        return carry;
      }
      const value = String(node.props[name]);
      if (value === "") {
        return carry + ` ${name}`;
      } else {
        return carry + ` ${name}="${escape2(value)}"`;
      }
    }, "");
    return `<${node.type}${attrs}>`;
  }
  function renderTagChildren(node) {
    return typeof node.props.children === "string" ? node.props.children : node.props.children.reduce((html, child) => html + renderTag(child), "");
  }
  function renderTag(node) {
    let html = renderTagStart(node);
    if (node.props.children) {
      html += renderTagChildren(node);
    }
    if (node.props.dangerouslySetInnerHTML) {
      html += node.props.dangerouslySetInnerHTML.__html;
    }
    if (!isUnaryTag(node)) {
      html += `</${node.type}>`;
    }
    return html;
  }
  function ensureNodeHasInertiaProp(node) {
    return import_react7.default.cloneElement(node, {
      inertia: node.props["head-key"] !== void 0 ? node.props["head-key"] : ""
    });
  }
  function renderNode(node) {
    return renderTag(ensureNodeHasInertiaProp(node));
  }
  function renderNodes(nodes) {
    const computed = import_react7.default.Children.toArray(nodes).filter((node) => node).map((node) => renderNode(node));
    if (title && !computed.find((tag) => tag.startsWith("<title"))) {
      computed.push(`<title inertia>${title}</title>`);
    }
    return computed;
  }
  if (isServer2) {
    provider.update(renderNodes(children));
  }
  return null;
};
var Head_default = Head;
var noop3 = () => void 0;
var Link = (0, import_react8.forwardRef)(
  ({
    children,
    as = "a",
    data = {},
    href,
    method: method2 = "get",
    preserveScroll = false,
    preserveState = null,
    replace: replace2 = false,
    only = [],
    except = [],
    headers = {},
    queryStringArrayFormat = "brackets",
    async = false,
    onClick = noop3,
    onCancelToken = noop3,
    onBefore = noop3,
    onStart = noop3,
    onProgress = noop3,
    onFinish = noop3,
    onCancel = noop3,
    onSuccess = noop3,
    onError = noop3,
    prefetch = false,
    cacheFor = 0,
    ...props
  }, ref) => {
    const [inFlightCount, setInFlightCount] = (0, import_react8.useState)(0);
    const hoverTimeout = (0, import_react8.useRef)(null);
    const _method = (0, import_react8.useMemo)(() => {
      return typeof href === "object" ? href.method : method2.toLowerCase();
    }, [href, method2]);
    const _as = (0, import_react8.useMemo)(() => {
      as = as.toLowerCase();
      return _method !== "get" ? "button" : as;
    }, [as, _method]);
    const mergeDataArray = (0, import_react8.useMemo)(
      () => mergeDataIntoQueryString(
        _method,
        typeof href === "object" ? href.url : href || "",
        data,
        queryStringArrayFormat
      ),
      [href, _method, data, queryStringArrayFormat]
    );
    const url = (0, import_react8.useMemo)(() => mergeDataArray[0], [mergeDataArray]);
    const _data = (0, import_react8.useMemo)(() => mergeDataArray[1], [mergeDataArray]);
    const baseParams = (0, import_react8.useMemo)(
      () => ({
        data: _data,
        method: _method,
        preserveScroll,
        preserveState: preserveState ?? _method !== "get",
        replace: replace2,
        only,
        except,
        headers,
        async
      }),
      [_data, _method, preserveScroll, preserveState, replace2, only, except, headers, async]
    );
    const visitParams = (0, import_react8.useMemo)(
      () => ({
        ...baseParams,
        onCancelToken,
        onBefore,
        onStart(event) {
          setInFlightCount((count) => count + 1);
          onStart(event);
        },
        onProgress,
        onFinish(event) {
          setInFlightCount((count) => count - 1);
          onFinish(event);
        },
        onCancel,
        onSuccess,
        onError
      }),
      [baseParams, onCancelToken, onBefore, onStart, onProgress, onFinish, onCancel, onSuccess, onError]
    );
    const doPrefetch = () => {
      router.prefetch(url, baseParams, { cacheFor: cacheForValue });
    };
    const prefetchModes = (0, import_react8.useMemo)(
      () => {
        if (prefetch === true) {
          return ["hover"];
        }
        if (prefetch === false) {
          return [];
        }
        if (Array.isArray(prefetch)) {
          return prefetch;
        }
        return [prefetch];
      },
      Array.isArray(prefetch) ? prefetch : [prefetch]
    );
    const cacheForValue = (0, import_react8.useMemo)(() => {
      if (cacheFor !== 0) {
        return cacheFor;
      }
      if (prefetchModes.length === 1 && prefetchModes[0] === "click") {
        return 0;
      }
      return 3e4;
    }, [cacheFor, prefetchModes]);
    (0, import_react8.useEffect)(() => {
      return () => {
        clearTimeout(hoverTimeout.current);
      };
    }, []);
    (0, import_react8.useEffect)(() => {
      if (prefetchModes.includes("mount")) {
        setTimeout(() => doPrefetch());
      }
    }, prefetchModes);
    const regularEvents = {
      onClick: (event) => {
        onClick(event);
        if (shouldIntercept(event)) {
          event.preventDefault();
          router.visit(url, visitParams);
        }
      }
    };
    const prefetchHoverEvents = {
      onMouseEnter: () => {
        hoverTimeout.current = window.setTimeout(() => {
          doPrefetch();
        }, 75);
      },
      onMouseLeave: () => {
        clearTimeout(hoverTimeout.current);
      },
      onClick: regularEvents.onClick
    };
    const prefetchClickEvents = {
      onMouseDown: (event) => {
        if (shouldIntercept(event)) {
          event.preventDefault();
          doPrefetch();
        }
      },
      onMouseUp: (event) => {
        event.preventDefault();
        router.visit(url, visitParams);
      },
      onClick: (event) => {
        onClick(event);
        if (shouldIntercept(event)) {
          event.preventDefault();
        }
      }
    };
    const elProps = (0, import_react8.useMemo)(
      () => ({
        a: { href: url },
        button: { type: "button" }
      }),
      [url]
    );
    return (0, import_react8.createElement)(
      _as,
      {
        ...props,
        ...elProps[_as] || {},
        ref,
        ...(() => {
          if (prefetchModes.includes("hover")) {
            return prefetchHoverEvents;
          }
          if (prefetchModes.includes("click")) {
            return prefetchClickEvents;
          }
          return regularEvents;
        })(),
        "data-loading": inFlightCount > 0 ? "" : void 0
      },
      children
    );
  }
);
Link.displayName = "InertiaLink";
var Link_default = Link;
function useRemember(initialState, key) {
  const [state, setState] = (0, import_react10.useState)(() => {
    const restored = router.restore(key);
    return restored !== void 0 ? restored : initialState;
  });
  (0, import_react10.useEffect)(() => {
    router.remember(state, key);
  }, [state, key]);
  return [state, setState];
}
function useForm(rememberKeyOrInitialValues, maybeInitialValues) {
  const isMounted = (0, import_react9.useRef)(null);
  const rememberKey = typeof rememberKeyOrInitialValues === "string" ? rememberKeyOrInitialValues : null;
  const [defaults2, setDefaults] = (0, import_react9.useState)(
    (typeof rememberKeyOrInitialValues === "string" ? maybeInitialValues : rememberKeyOrInitialValues) || {}
  );
  const cancelToken = (0, import_react9.useRef)(null);
  const recentlySuccessfulTimeoutId = (0, import_react9.useRef)(null);
  const [data, setData] = rememberKey ? useRemember(defaults2, `${rememberKey}:data`) : (0, import_react9.useState)(defaults2);
  const [errors, setErrors] = rememberKey ? useRemember({}, `${rememberKey}:errors`) : (0, import_react9.useState)({});
  const [hasErrors, setHasErrors] = (0, import_react9.useState)(false);
  const [processing, setProcessing] = (0, import_react9.useState)(false);
  const [progress3, setProgress] = (0, import_react9.useState)(null);
  const [wasSuccessful, setWasSuccessful] = (0, import_react9.useState)(false);
  const [recentlySuccessful, setRecentlySuccessful] = (0, import_react9.useState)(false);
  const transform2 = (0, import_react9.useRef)((data2) => data2);
  const isDirty = (0, import_react9.useMemo)(() => !isEqual(data, defaults2), [data, defaults2]);
  (0, import_react9.useEffect)(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);
  const submit = (0, import_react9.useCallback)(
    (...args) => {
      const objectPassed = typeof args[0] === "object";
      const method2 = objectPassed ? args[0].method : args[0];
      const url = objectPassed ? args[0].url : args[1];
      const options = (objectPassed ? args[1] : args[2]) ?? {};
      const _options = {
        ...options,
        onCancelToken: (token) => {
          cancelToken.current = token;
          if (options.onCancelToken) {
            return options.onCancelToken(token);
          }
        },
        onBefore: (visit) => {
          setWasSuccessful(false);
          setRecentlySuccessful(false);
          clearTimeout(recentlySuccessfulTimeoutId.current);
          if (options.onBefore) {
            return options.onBefore(visit);
          }
        },
        onStart: (visit) => {
          setProcessing(true);
          if (options.onStart) {
            return options.onStart(visit);
          }
        },
        onProgress: (event) => {
          setProgress(event);
          if (options.onProgress) {
            return options.onProgress(event);
          }
        },
        onSuccess: (page2) => {
          if (isMounted.current) {
            setProcessing(false);
            setProgress(null);
            setErrors({});
            setHasErrors(false);
            setWasSuccessful(true);
            setRecentlySuccessful(true);
            setDefaults(cloneDeep(data));
            recentlySuccessfulTimeoutId.current = setTimeout(() => {
              if (isMounted.current) {
                setRecentlySuccessful(false);
              }
            }, 2e3);
          }
          if (options.onSuccess) {
            return options.onSuccess(page2);
          }
        },
        onError: (errors2) => {
          if (isMounted.current) {
            setProcessing(false);
            setProgress(null);
            setErrors(errors2);
            setHasErrors(true);
          }
          if (options.onError) {
            return options.onError(errors2);
          }
        },
        onCancel: () => {
          if (isMounted.current) {
            setProcessing(false);
            setProgress(null);
          }
          if (options.onCancel) {
            return options.onCancel();
          }
        },
        onFinish: (visit) => {
          if (isMounted.current) {
            setProcessing(false);
            setProgress(null);
          }
          cancelToken.current = null;
          if (options.onFinish) {
            return options.onFinish(visit);
          }
        }
      };
      if (method2 === "delete") {
        router.delete(url, { ..._options, data: transform2.current(data) });
      } else {
        router[method2](url, transform2.current(data), _options);
      }
    },
    [data, setErrors, transform2]
  );
  const setDataFunction = (0, import_react9.useCallback)(
    (keyOrData, maybeValue) => {
      if (typeof keyOrData === "string") {
        setData((data2) => set2(cloneDeep(data2), keyOrData, maybeValue));
      } else if (typeof keyOrData === "function") {
        setData((data2) => keyOrData(data2));
      } else {
        setData(keyOrData);
      }
    },
    [setData]
  );
  const [dataAsDefaults, setDataAsDefaults] = (0, import_react9.useState)(false);
  const setDefaultsFunction = (0, import_react9.useCallback)(
    (fieldOrFields, maybeValue) => {
      if (typeof fieldOrFields === "undefined") {
        setDefaults(data);
        setDataAsDefaults(true);
      } else {
        setDefaults((defaults22) => {
          return typeof fieldOrFields === "string" ? set2(cloneDeep(defaults22), fieldOrFields, maybeValue) : Object.assign(cloneDeep(defaults22), fieldOrFields);
        });
      }
    },
    [data, setDefaults]
  );
  (0, import_react9.useLayoutEffect)(() => {
    if (!dataAsDefaults) {
      return;
    }
    if (isDirty) {
      setDefaults(data);
    }
    setDataAsDefaults(false);
  }, [dataAsDefaults]);
  const reset = (0, import_react9.useCallback)(
    (...fields) => {
      if (fields.length === 0) {
        setData(defaults2);
      } else {
        setData(
          (data2) => fields.filter((key) => has(defaults2, key)).reduce(
            (carry, key) => {
              return set2(carry, key, get(defaults2, key));
            },
            { ...data2 }
          )
        );
      }
    },
    [setData, defaults2]
  );
  const setError = (0, import_react9.useCallback)(
    (fieldOrFields, maybeValue) => {
      setErrors((errors2) => {
        const newErrors = {
          ...errors2,
          ...typeof fieldOrFields === "string" ? { [fieldOrFields]: maybeValue } : fieldOrFields
        };
        setHasErrors(Object.keys(newErrors).length > 0);
        return newErrors;
      });
    },
    [setErrors, setHasErrors]
  );
  const clearErrors = (0, import_react9.useCallback)(
    (...fields) => {
      setErrors((errors2) => {
        const newErrors = Object.keys(errors2).reduce(
          (carry, field) => ({
            ...carry,
            ...fields.length > 0 && !fields.includes(field) ? { [field]: errors2[field] } : {}
          }),
          {}
        );
        setHasErrors(Object.keys(newErrors).length > 0);
        return newErrors;
      });
    },
    [setErrors, setHasErrors]
  );
  const resetAndClearErrors = (0, import_react9.useCallback)(
    (...fields) => {
      reset(...fields);
      clearErrors(...fields);
    },
    [reset, clearErrors]
  );
  const createSubmitMethod = (method2) => (url, options) => {
    submit(method2, url, options);
  };
  const getMethod = (0, import_react9.useCallback)(createSubmitMethod("get"), [submit]);
  const post = (0, import_react9.useCallback)(createSubmitMethod("post"), [submit]);
  const put = (0, import_react9.useCallback)(createSubmitMethod("put"), [submit]);
  const patch = (0, import_react9.useCallback)(createSubmitMethod("patch"), [submit]);
  const deleteMethod = (0, import_react9.useCallback)(createSubmitMethod("delete"), [submit]);
  const cancel = (0, import_react9.useCallback)(() => {
    if (cancelToken.current) {
      cancelToken.current.cancel();
    }
  }, []);
  const transformFunction = (0, import_react9.useCallback)((callback) => {
    transform2.current = callback;
  }, []);
  return {
    data,
    setData: setDataFunction,
    isDirty,
    errors,
    hasErrors,
    processing,
    progress: progress3,
    wasSuccessful,
    recentlySuccessful,
    transform: transformFunction,
    setDefaults: setDefaultsFunction,
    reset,
    setError,
    clearErrors,
    resetAndClearErrors,
    submit,
    get: getMethod,
    post,
    put,
    patch,
    delete: deleteMethod,
    cancel
  };
}
function usePoll(interval, requestOptions = {}, options = {
  keepAlive: false,
  autoStart: true
}) {
  const pollRef = (0, import_react11.useRef)(
    router.poll(interval, requestOptions, {
      ...options,
      autoStart: false
    })
  );
  (0, import_react11.useEffect)(() => {
    if (options.autoStart ?? true) {
      pollRef.current.start();
    }
    return () => pollRef.current.stop();
  }, []);
  return {
    stop: pollRef.current.stop,
    start: pollRef.current.start
  };
}
function usePrefetch(options = {}) {
  const cached = typeof window === "undefined" ? null : router.getCached(window.location.pathname, options);
  const inFlight = typeof window === "undefined" ? null : router.getPrefetching(window.location.pathname, options);
  const [lastUpdatedAt, setLastUpdatedAt] = (0, import_react12.useState)(cached?.staleTimestamp || null);
  const [isPrefetching, setIsPrefetching] = (0, import_react12.useState)(inFlight !== null);
  const [isPrefetched, setIsPrefetched] = (0, import_react12.useState)(cached !== null);
  (0, import_react12.useEffect)(() => {
    const onPrefetchingListener = router.on("prefetching", (e) => {
      if (e.detail.visit.url.pathname === window.location.pathname) {
        setIsPrefetching(true);
      }
    });
    const onPrefetchedListener = router.on("prefetched", (e) => {
      if (e.detail.visit.url.pathname === window.location.pathname) {
        setIsPrefetching(false);
        setIsPrefetched(true);
        setLastUpdatedAt(e.detail.fetchedAt);
      }
    });
    return () => {
      onPrefetchedListener();
      onPrefetchingListener();
    };
  }, []);
  return {
    lastUpdatedAt,
    isPrefetching,
    isPrefetched,
    flush: () => router.flush(window.location.pathname, options)
  };
}
var WhenVisible = ({ children, data, params, buffer, as, always, fallback }) => {
  always = always ?? false;
  as = as ?? "div";
  fallback = fallback ?? null;
  const [loaded, setLoaded] = (0, import_react13.useState)(false);
  const hasFetched = (0, import_react13.useRef)(false);
  const fetching = (0, import_react13.useRef)(false);
  const ref = (0, import_react13.useRef)(null);
  const getReloadParams = (0, import_react13.useCallback)(() => {
    if (data) {
      return {
        only: Array.isArray(data) ? data : [data]
      };
    }
    if (!params) {
      throw new Error("You must provide either a `data` or `params` prop.");
    }
    return params;
  }, [params, data]);
  (0, import_react13.useEffect)(() => {
    if (!ref.current) {
      return;
    }
    const observer = new IntersectionObserver(
      (entries) => {
        if (!entries[0].isIntersecting) {
          return;
        }
        if (!always && hasFetched.current) {
          observer.disconnect();
        }
        if (fetching.current) {
          return;
        }
        hasFetched.current = true;
        fetching.current = true;
        const reloadParams = getReloadParams();
        router.reload({
          ...reloadParams,
          onStart: (e) => {
            fetching.current = true;
            reloadParams.onStart?.(e);
          },
          onFinish: (e) => {
            setLoaded(true);
            fetching.current = false;
            reloadParams.onFinish?.(e);
            if (!always) {
              observer.disconnect();
            }
          }
        });
      },
      {
        rootMargin: `${buffer || 0}px`
      }
    );
    observer.observe(ref.current);
    return () => {
      observer.disconnect();
    };
  }, [ref, getReloadParams, buffer]);
  if (always || !loaded) {
    return (0, import_react13.createElement)(
      as,
      {
        props: null,
        ref
      },
      loaded ? children : fallback
    );
  }
  return loaded ? children : null;
};
WhenVisible.displayName = "InertiaWhenVisible";
var WhenVisible_default = WhenVisible;
var router3 = router;
export {
  Deferred_default as Deferred,
  Head_default as Head,
  Link_default as Link,
  WhenVisible_default as WhenVisible,
  createInertiaApp,
  router3 as router,
  useForm,
  usePage,
  usePoll,
  usePrefetch,
  useRemember
};
/*! Bundled license information:

@inertiajs/core/dist/index.esm.js:
  (* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
   * @license MIT *)
*/
//# sourceMappingURL=@inertiajs_react.js.map
