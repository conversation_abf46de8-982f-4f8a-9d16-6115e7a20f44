<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spa<PERSON>\Permission\Traits\HasRoles;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'address',
        'date_of_birth',
        'gender',
        'occupation',
        'emergency_contact_name',
        'emergency_contact_phone',
        'profile_photo',
        'membership_status',
        'membership_start_date',
        'membership_end_date',
        'is_admin',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'date_of_birth' => 'date',
            'membership_start_date' => 'date',
            'membership_end_date' => 'date',
            'is_admin' => 'boolean',
        ];
    }

    // Relationships
    public function memberApplications()
    {
        return $this->hasMany(MemberApplication::class, 'reviewed_by');
    }

    public function events()
    {
        return $this->hasMany(Event::class, 'created_by');
    }

    public function announcements()
    {
        return $this->hasMany(Announcement::class, 'created_by');
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function verifiedPayments()
    {
        return $this->hasMany(Payment::class, 'verified_by');
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    // Helper methods
    public function isAdmin()
    {
        return $this->is_admin || $this->hasRole('admin');
    }

    public function isMember()
    {
        return $this->membership_status === 'active';
    }
}
