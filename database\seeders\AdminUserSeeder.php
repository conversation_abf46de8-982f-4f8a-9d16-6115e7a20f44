<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $memberRole = Role::firstOrCreate(['name' => 'member']);

        // Create permissions
        $permissions = [
            'manage_users',
            'manage_events',
            'manage_announcements',
            'manage_payments',
            'manage_applications',
            'view_dashboard',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign all permissions to admin role
        $adminRole->givePermissionTo($permissions);

        // Assign limited permissions to member role
        $memberRole->givePermissionTo(['view_dashboard']);

        // Create admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Club Administrator',
                'password' => Hash::make('password'),
                'is_admin' => true,
                'membership_status' => 'active',
                'membership_start_date' => now(),
                'email_verified_at' => now(),
            ]
        );

        $admin->assignRole('admin');

        // Create sample member
        $member = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'John Doe',
                'password' => Hash::make('password'),
                'phone' => '+63 ************',
                'address' => '123 Main St, Manila, Philippines',
                'date_of_birth' => '1990-01-01',
                'gender' => 'male',
                'occupation' => 'Software Engineer',
                'emergency_contact_name' => 'Jane Doe',
                'emergency_contact_phone' => '+63 ************',
                'membership_status' => 'active',
                'membership_start_date' => now(),
                'email_verified_at' => now(),
            ]
        );

        $member->assignRole('member');
    }
}
